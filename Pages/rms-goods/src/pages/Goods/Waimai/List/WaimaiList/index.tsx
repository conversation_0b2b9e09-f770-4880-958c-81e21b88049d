import * as React from 'react';
import { observer } from 'mobx-react';
import WaimaiModel from '@rms-goods/root/src/models/waimai/goods';
import { noEmpty } from '@rms-goods/utils/paramParse';
import { RouteComponentPropsWithQuery, historyPush } from '@mtfe/next-router';
import { historyPush as historyPushV2, withRouteLifeCycle } from '@mtfe/next-router-v2';
import { Select, Input, Spin, Result, ITreeNodeType, showModal, message, FormX } from '@mtfe/sjst-antdx';
import { Button, ExtraInfo, TableReportRef } from '@mtfe/sjst-antdx-saas';
import { TreeTableReport } from '@rms-goods/components/TableReport';
import { createRouteTabs, TabPane } from '@mtfe/next-biz/es/components/MenuTab';
import { ensureWmOpen, fetchWaimaiSpuList2, WmBindPath } from '@rms-goods/services/waimai';
import listUtil from '@rms-goods/utils/goods/spuListToSkuList';
import formUtil, { PriceParseTypeEnum } from '@rms-goods/utils/goods/goodsFormValue';
import EditableCell from '@rms-goods/components/EditableCell';
import { IV } from '@mtfe/sjst-antdx/es/common/utils';
import { SyncStatusEnum, WaimaiSourceEnum } from '@rms-goods/services/waimai/interface';
import {
  TaxRateSwitchStatusEnum,
  IsNotSingleEnum,
  WmTabPanelEnum,
  WmType,
  WmBindActionEnum,
  WmPoiPurchase,
  WmSource,
  SWITTCH_TYPE,
  SWITCH_STATE,
  BatchActionRouteEnum,
  TsTabPanelEnum,
  WmStatus,
  SpuParseType,
  ASSOCIATE_MODE,
  COMBO_VERSION,
  CommonConfirmEnum,
} from '@typings/GoodsTypings/enum';
import { poiConfig } from '@mtfe/next-biz/es/utils/managePoi';
import { Org, BusinessModule } from '@mtfe/next-biz/es/components/Org/Poi';
import { PoiSelector, baseCostomFilter } from '@mtfe/next-biz/es/components/Waimai/PoiSelector';
import { historyPathnamePush, NAVIGATORTYPE, withRouter, historyPush as _historyPush, LifeCycleProps } from '@mtfe/next-router-v2';
import {
  WaimaiPermissionCode,
  editPermissionOptions,
  importPermissionOptions,
  getWmPermissionCode,
  WMActionEnum,
} from '@rms-goods/root/src/utils/permissionCode';
import { wrapContext, UserService } from '@mtfe/next-biz/src/contexts/user';
import { Permission } from '@mtfe/next-biz/src/components/Permission';
import { getUniqueUrlParams } from '@rms-goods/root/src/utils/url';
import { checkhasPermissionSync } from '@mtfe/next-biz/src/services/permission';
import { WmSpuNameModel } from '@rms-goods/models/goods/index';
import { TIP_TEXT } from './constants';
import { getWaimaiListColumns, IOptions } from './Column';
import SelectPoiModal from './SelectPoiModal';
import { batchWaimaiConfig, chainWaimaiConfig, poiWaimaiConfig } from '../../BatchAction/batchRouteConfig';
import { BatchConfigProps, BatchModalProps } from '../../BatchAction/type';
import { PkgPubTabsKey } from '@rms-goods/root/src/pages/GoodsPublish/WaimaiPkgPub/constant';
import {
  getWaimaAnalytics,
  getWaimaiPermissionCode,
  getWaimaiComboPermissionCode,
  getBidByOption,
  getWmQueryInfo,
  getRouterConfig,
  isShowComboCreateBtn,
} from '../../utils';
import { PublishType } from '@mtfe/next-biz/es/services/goodsCategory';
import { SpuTypeEnum } from '@mtfe/next-biz/es/services/types';
import { PoiModalSelector } from '@mtfe/next-biz/es/components/Waimai';
// 5.21.10引入
import { BindWmChainList } from '../BindWmChainList/entry';
import { listModel } from '@rms-goods/root/src/models/waimaiBind';

import GoodSort from '@rms-goods/components/WmGoodsSort/Chain';

import './index.less';
import { CanOperableEnum, ShowOpenWaimaiModal, SourceTypeEnum } from '@mtfe/next-biz/es/components/PurchasWaimaiModal';
import { WmRecord } from '../../../types';
import { moduleClick } from '@mtfe/next-biz/es/utils/analytics';
import AB, { ABtestEnum, isAB } from '@rms-goods/root/src/components/AB';
import { arr2Map } from '@rms-goods/root/src/utils/map';
import createBindWmListManyForOne from '../BindWmList/BindWmListManyForOne';
import SessionPersistence from '@rms-goods/root/src/utils/sessionPersistence';
import WmPrintSort from '@rms-goods/root/src/components/WmPrintSort';
import { getSwitchConfigMap } from '@rms-goods/root/src/utils/goods/getSwitchConfigMap';
import { SeniorPrintModal } from '@rms-goods/root/src/models/printSettings';
import { batchGetSwitchConfig } from '@mtfe/next-biz/es/services/config';
import { SWITCH_KEY, SWITCH_KEY_ENUM } from '@typings/GoodsTypings/switch';
import {
  getRelationPoiId,
  getVirtualPoiId,
  usedPoiId,
  getMainPoiIdByOrg,
  checkVirtual,
  getOrgId,
  isVirtualPage,
  isVirtualPoi,
  onPoiSelectChange,
} from '@rms-goods/utils/poiVirtual';
import isManagePoiUtil from '@mtfe/next-biz/es/utils/managePoi';
import { TargetTenantIdEnum, isNewTenant } from '@rms-goods/root/src/utils/tenant';
import { AutomationIdEnum, FunctionIdEnum } from '@typings/QAAutomationTypings/enum';
import listStandard from '@rms-goods/root/src/utils/listStandard';
import { IColumnsType } from '@rms-goods/root/src/utils/fieldSetting/interface';
import FieldSettingButton from '@rms-goods/root/src/components/FieldSetting/FieldSettingButton';

import BindChainAttrList from '../BindChainAttrList';
import { BindChainAttrListTabRef } from '../BindChainAttrList/AttrList';
import { WaimaiMenuCodes } from '@rms-goods/root/src/utils/menuCode';
import { ISwitchStatus, SwitchStatusType } from '../../../Tangshi/List/Application';
import { getGoodsService } from '@rms-goods/root/src/services/goods';
import { AttrEntranceEnum } from './const';
import { isShow } from '../../../utils';
import BatchSetTemplate from '../../BatchAction/BatchSetTemplate';
import { AnalyticsEnum, getAnalyticsCode } from '../../PinCanManager/const/analytics';
import { reportNetCount, reportNetErr, reportNetTime, reportNetSuccess } from '@mtfe/next-biz/src/utils/owl-monitor/rms-goods/reportNetErr';

import styles from './index.module.less';
import { getMTSideIsUse, getSideName } from '@rms-goods/root/src/utils/goodsSide/mTSideIsUtils';

const Option = Select.Option;
const Tabs = createRouteTabs();

const SpuTypes = {
  饿了么套餐: `[${COMBO_VERSION.ELE_NEW}]`,
  美团套餐: `[${COMBO_VERSION.MT_NEW}]`,
  收银套餐: `[${COMBO_VERSION.CASHIER_OLD}, ${COMBO_VERSION.CASHIER_NEW}]`,
};

interface IQuery {
  spuName?: string;
  numMnemonicCode?: string;
  categoryId?: string;
  spuTypes?: number | string;
  isNotSingle?: IsNotSingleEnum;
  org?: Org | number;
}

interface State {
  // showGoodsSortModal: boolean;
  // expandedRowKeys: number[];
  categoryTree: ITreeNodeType[];
  selectedKeys: string[];
  expandNodes: string[];
  batchModalShow?: boolean;
  /** 是否购买外卖服务包 */
  buyStatus?: boolean;
  wmListTabText: string;
  wmType: WmType;
  activeKey: WmTabPanelEnum | TsTabPanelEnum | string;
  batchActionHiddenMap: { [k: string]: boolean };
  poiSelectorVisible: boolean;
  selectedSpu?: WmRecord;
  // 是否味千, 仅个性化定制
  abStrategyMap?: { [k: string]: boolean };
  switchStatusMap?: { [k: string]: boolean };
  showWaimaiGoodsSelector: boolean;
  //老业务开关状态
  oldSwitchStatusMap?: SwitchStatusType;
  //堂食与外卖菜关联模式
  associateMode?: ASSOCIATE_MODE;
  isElemeNewTenant?: boolean;
  /** 判断是否在当前页面 */
  isCurrentRoute?: boolean;
  /** 展示设置模板弹窗 */
  showBatchSetTemplate?: boolean;
  /** 通过字段设置得到的列配置 */
  allColumns: IColumnsType<WmRecord>[];
}

interface Props extends RouteComponentPropsWithQuery, LifeCycleProps {
  /**
   * 是否堂食菜品引用, 堂食引用的外卖组件不展示tab
   */
  isTangshi?: boolean;
  userService: UserService;
}

/** 多对一美团外卖列表 */
const BindMtGoodsListManyForOne = createBindWmListManyForOne(WaimaiSourceEnum.MEITUAN);
/** 多对一饿了么外卖列表 */
const BindElmGoodsListManyForOne = createBindWmListManyForOne(WaimaiSourceEnum.ELEME);

const PersistenceModuleIdMap = {
  [WaimaiSourceEnum.MEITUAN]: listStandard.PersistenceModule.外卖菜品管理列表_美团,
  [WaimaiSourceEnum.ELEME]: listStandard.PersistenceModule.外卖菜品管理列表_饿了么,
};

const PersistenceModuleIdMapTangShi = {
  [WaimaiSourceEnum.MEITUAN]: listStandard.PersistenceModule.外卖菜品管理列表_美团_堂食,
  [WaimaiSourceEnum.ELEME]: listStandard.PersistenceModule.外卖菜品管理列表_饿了么_堂食,
};

/**
 * 此页面isManagePoi/isChainPage 通过entry传参判断  与URL managePoi=true无关
 */
const WaimaiGoodsList = (
  source = WaimaiSourceEnum.MEITUAN,
  /** 通过entry传参判断：穿透 */
  isManagePoi?: boolean,
  /** 通过entry传参判断：总部视角的总部页面 isChain&&!isManagePoi 【取反为：门店|穿透】 */
  isChainPage?: boolean
) => {
  const WaimaiAnalytics = getWaimaAnalytics(source, isManagePoi, isChainPage);
  @observer
  class List extends React.Component<Props, State> {
    treeReportRef?: TableReportRef | null = null;
    /** 用于处理tab切换时属性绑定tab的路由参数reset */
    bindChainAttrListTabRef = React.createRef<BindChainAttrListTabRef>();

    formRef: { current?: FormX } = { current: undefined };

    BatchModal?: React.ComponentClass<BatchModalProps>;

    private analytics = new WaimaiAnalytics({
      mtId: 'c_eco_07o1dwa4',
      eleId: 'c_eco_76qt9plo',
      manageMtId: 'c_eco_yevaxmw2',
      manageEleId: 'c_eco_3qf35qd7',
      chainMtId: 'c_eco_tl9s76vj',
      chainEleId: 'c_eco_5vym4sbm',
    });

    /**
     * 埋点
     */
    isFirst = true;
    /**
     * 是否门店 [单店&&连锁门店]
     */
    isSingle: boolean;
    /**
     * 穿透/门店 视角展示tab
     */
    showTabs: boolean;
    /**
     * 只有穿透视角展示「门店与集团外卖菜关联」页
     */
    showWmTabs?: boolean;
    /**
     ** 用于判断美团是否支持小料功能
     */
    isUseSide?: boolean;
    businessSwitchList: SWITTCH_TYPE[];

    PERSISTENCE_KEY: string;

    sessionPersistence: SessionPersistence;

    constructor(props: Props, private model: WaimaiModel) {
      super(props);
      // !isChainPage && !this.props.isTangshi && checkVirtual() 初始化并不能拿到url上managePoi参数因此通过外部传参判断
      this.model = new WaimaiModel(source, isManagePoi || (!isChainPage && !this.props.isTangshi && checkVirtual()), isChainPage, this.org);
      console.log('model', this.model);
      this.isSingle = !isManagePoi && !isChainPage;
      this.showTabs = !isChainPage;
      this.showWmTabs = isManagePoi && !isChainPage;
      this.PERSISTENCE_KEY = !!isManagePoi
        ? `__$goodsListQuery_ManagePoi_WM${window.location.hash}`
        : `__$goodsListQuery_WM'${window.location.hash}`;
      this.sessionPersistence = new SessionPersistence(this.PERSISTENCE_KEY);
      this.state = {
        categoryTree: [],
        selectedKeys: [],
        expandNodes: [],
        buyStatus: false,
        wmListTabText: `门店${source === WaimaiSourceEnum.MEITUAN ? '美团' : '饿了么'}外卖菜品`,
        wmType: WmType.ALL,
        // 是否来菜品未关联页
        activeKey: new URLSearchParams(this.props.location.search).get('activeTab') || '1',
        batchActionHiddenMap: {
          PRINT_SORT: true,
        },
        poiSelectorVisible: false,
        abStrategyMap: { [ABtestEnum.味千]: false, [ABtestEnum.外卖菜品关联堂食菜品多对一]: false },
        switchStatusMap: {},
        showWaimaiGoodsSelector: false,
        isCurrentRoute: true,
        showBatchSetTemplate: false,
        allColumns: [],
      };
      this.businessSwitchList = [];
      if (this.isSingle) {
        this.businessSwitchList = [SWITTCH_TYPE.WM_PRINT];
      }
    }

    /** 设置模板操作的菜品 */
    operateRecord?: WmRecord;

    poiRef = {
      poiId: poiConfig.poiId,
      orgId: poiConfig.orgId,
      virtualPoiId: poiConfig.virtualPoiId,
      relationPoiId: getRelationPoiId(),
    };

    get org() {
      return {
        id: poiConfig.orgId,
        poiId: poiConfig.poiId,
        virtualPoiId: poiConfig.virtualPoiId,
      } as Org & { virtualPoiId?: number };
    }

    get isSingleHasVirNotTangshi() {
      return this.isSingle && !this.props.isTangshi && checkVirtual();
    }

    get isEditPermission() {
      return getWaimaiPermissionCode(editPermissionOptions, source, isManagePoi, isChainPage).length > 0;
    }

    get showBindWmChainTab() {
      // 对应模块下集团外卖菜品有菜品档案&存在角色权限，则显示Tab
      const factor = source === WaimaiSourceEnum.MEITUAN ? WmType.ELE : WmType.MT;
      const existGoods = this.state.wmType !== WmType.NONE && this.state.wmType !== factor;
      return existGoods;
    }

    get persistenceModuleId() {
      return this.props.isTangshi
        ? PersistenceModuleIdMapTangShi[source as keyof typeof PersistenceModuleIdMapTangShi]
        : PersistenceModuleIdMap[source as keyof typeof PersistenceModuleIdMap];
    }

    get isMT() {
      return source === WaimaiSourceEnum.MEITUAN;
    }

    async componentDidMount() {
      this.onRouteChange();

      // 增加味千AB灰度判断
      // 根据打印开关判断是否弱化档口概念
      const [switchStatusRes, abStrategyMap] = await Promise.all([
        this.getSwitchStatus(),
        this.setAbStrategyMapAndInitTenant(),
        SeniorPrintModal.Init(),
      ]);

      this.onFieldsSetting(true, {
        abStrategyMap,
        switchStatusMap: switchStatusRes.switchStatusMap ?? void 0,
        taxRateSwitchStatus: switchStatusRes.taxRateSwitchStatus,
      });

      if (this.isSingle) {
        // 门店视角  门店视角校验购买状态
        this.initStatus();
      } else {
        await this.model.init();
        await this.ensureWmStatus();
      }
      this.isUseSide = await getMTSideIsUse(!!isChainPage, isChainPage ? undefined : usedPoiId());
    }

    /**
     * 字段设置
     * 外卖视角集团、门店、堂食视角的字段设置按钮分别写在了各自页面中
     * @param isInit
     * @param allColumns
     * @param options 白名单、开关等state
     */
    onFieldsSetting = async (isInit?: boolean, options?: Pick<IOptions, 'abStrategyMap' | 'switchStatusMap' | 'taxRateSwitchStatus'>) => {
      const showModal = !isInit;
      const columnsConfig = isInit && options ? this.getColumns(options) : this.state.allColumns;

      const currentConfig = await listStandard.fieldsSettingUtils.fieldsSetting({
        showModal,
        reportName: this.persistenceModuleId,
        initValue: columnsConfig,
      });

      this.setState({ allColumns: currentConfig });
    };

    onRouteChange = () => {
      if (isManagePoi || this.isSingleHasVirNotTangshi) {
        this.props.didEnter(() => {
          this.setState({ isCurrentRoute: true });
        });

        this.props.onLeave(() => {
          this.setState({ isCurrentRoute: false });
        });
      }
    };

    getOldSwitchStatusMap = async () => {
      //老业务开关数据处理
      const oldSwitchStatus = await (await getGoodsService()).batchQuerySwitch({
        businessSwitchList: [SWITTCH_TYPE.SSU_MGMT, SWITTCH_TYPE.SSU_MGMT_PATTERN],
      });
      const oldSwitchStatusMap: SwitchStatusType = {};
      oldSwitchStatus.forEach(({ type, status }: ISwitchStatus) => {
        oldSwitchStatusMap[type] = status;
      });
      this.setState({
        associateMode: (oldSwitchStatusMap[SWITTCH_TYPE.SSU_MGMT_PATTERN] as unknown) as ASSOCIATE_MODE,
        oldSwitchStatusMap,
      });
    };

    getSwitchStatus = async () => {
      // 获取开关状态
      const [switchStatusRes, taxSwitchRes] = await Promise.all([
        batchGetSwitchConfig([
          SWITCH_KEY[SWITCH_KEY_ENUM.CHAIN_DiSPLAY_ASSOCIATED_DISH],
          SWITCH_KEY[SWITCH_KEY_ENUM.CHAIN_WAIMAI_PRINT_SORT],
          SWITCH_KEY[SWITCH_KEY_ENUM.POI_WAIMAI_PRINT_SORT],
          SWITCH_KEY[SWITCH_KEY_ENUM.POI_OLD_WAIMAI_PRINT_SORT],
          SWITCH_KEY[SWITCH_KEY_ENUM.拼好饭菜品管理],
          SWITCH_KEY[SWITCH_KEY_ENUM.拼团菜品管理],
          SWITCH_KEY[SWITCH_KEY_ENUM.美团收银套餐],
          SWITCH_KEY[SWITCH_KEY_ENUM.启用外卖属性库],
          SWITCH_KEY[SWITCH_KEY_ENUM.使用堂食外卖关联],
          SWITCH_KEY[SWITCH_KEY_ENUM.使用集团外卖关联],
          SWITCH_KEY[SWITCH_KEY_ENUM.管理自定义SKU码],
        ]),
        this.model.fetchTaxAndDepartList(),
      ]);
      let switchStatusMap: Record<string, boolean> | null = null;
      if (switchStatusRes?.length) {
        switchStatusMap = getSwitchConfigMap(switchStatusRes);

        this.setState({
          switchStatusMap: { ...this.state.switchStatusMap, ...switchStatusMap },
        });
      }
      //连锁门店 视角才请求老业务开关数据
      if (this.props.userService.isBranchPoi()) {
        this.getOldSwitchStatusMap();
      }

      return {
        switchStatusMap,
        taxRateSwitchStatus: taxSwitchRes?.taxRateSwitchStatus,
      };
    };

    setAbStrategyMapAndInitTenant = async () => {
      const [wqAB, isNewMTCombo, isManyForOne, isWmPkgPub, is568NewTenant, isEleNewCombo] = await Promise.all([
        isAB(ABtestEnum.味千),
        isAB(ABtestEnum.美团外卖新套餐),
        isAB(ABtestEnum.外卖菜品关联堂食菜品多对一),
        isAB(ABtestEnum.外卖整包),
        isNewTenant(TargetTenantIdEnum.饿了么移除老套餐),
        isAB(ABtestEnum.饿了么新套餐),
      ]);

      const abStrategyMap = {
        [ABtestEnum.味千]: !!wqAB,
        [ABtestEnum.美团外卖新套餐]: !!isNewMTCombo,
        [ABtestEnum.外卖菜品关联堂食菜品多对一]: !!isManyForOne,
        [ABtestEnum.外卖整包]: !!isWmPkgPub,
        [ABtestEnum.饿了么新套餐]: !!isEleNewCombo,
      };

      this.setState({
        abStrategyMap,
        isElemeNewTenant: is568NewTenant && source === WaimaiSourceEnum.ELEME,
      });

      return abStrategyMap;
    };

    initStatus = async () => {
      const sourceType = source === WaimaiSourceEnum.MEITUAN ? SourceTypeEnum.MT : SourceTypeEnum.ELE;
      const returnUrl = window.location.pathname + window.location.hash;
      const res = await ShowOpenWaimaiModal({ sourceType, returnUrl, poiIdList: [] }, () => {
        const path = source === WaimaiSourceEnum.MEITUAN ? WmBindPath.MT : WmBindPath.ELE;
        historyPush(window.history, path, true);
      });
      const buyStatus = res?.wmOperableList?.[0]?.canOperable === CanOperableEnum.CAN_OPERABLE;
      this.setState({ buyStatus });
      if (buyStatus) {
        await this.model.init();
        await this.ensureWmStatus();
      }
    };

    querySwitchStatus = async () => {
      const { businessSwitchList } = this;
      const statusList = (await this.model?.batchQuerySwitch({ businessSwitchList })) as { type: SWITTCH_TYPE; status: SWITCH_STATE }[];
      return arr2Map(statusList, ({ type, status }) => ({ key: type, val: status }));
    };

    async ensureWmStatus() {
      this.analytics.pageView();
      // 总部穿透管理或门店视角
      if (isChainPage) {
        this.getCategoryTree();
        this.model.syncStatus = SyncStatusEnum.SUCCESS;
      } else if (usedPoiId() || !isManagePoi) {
        if (!usedPoiId() && this.isSingleHasVirNotTangshi) {
          return;
        }
        // 需要判断穿透未选择门店的情况
        if (!isManagePoi && !(await ensureWmOpen(undefined, source, this.isSingleHasVirNotTangshi ? usedPoiId() : undefined))) {
          return;
        }
        this.getWmType();
        this.getDefaultActiveKey();
        this.model.isSyncStatusActionCalled = false;
        // 触发导入外卖菜同步操作
        await this.model.syncStatusAction(() => {
          this.refreshAll(true);
        });
      }
    }

    getWmType = async () => {
      const wmType = await listModel.getWmType();
      this.setState({ wmType });
    };

    getDefaultActiveKey = () => {
      const activeKey = window.history?.state?.state?.activeKey ?? undefined;
      if (activeKey) {
        this.setState({ activeKey });
      }
    };

    getBrandTree = async () => {
      const brandsList = await this.model.fetchBrandTree();
      const tree = brandsList.map(async (brand) => {
        const { key, title, isLeaf } = brand;
        const children = await this.model.fetchBrandCategorys({ brandId: key });
        return {
          key,
          title,
          isLeaf,
          children,
        };
      });
      return tree;
    };

    getCategoryTree = async (isSkipCache?: boolean) => {
      const tree = isChainPage ? await this.getBrandTree() : await this.model.fetchCategoryTree();
      Promise.all(tree).then((categoryTree: ITreeNodeType[]) => {
        isChainPage && categoryTree.unshift({ key: 'all', title: '全部', isLeaf: true });
        // 修复分类树展示错误，逻辑：当从其他页跳转回来时，取sessionStorage中的值，当在本页刷新时，取tablereport中的现有值
        let keys;

        const { selectedKeys, expandKeys } = listStandard.getCategoryTreeQuery({
          moduleKey: this.persistenceModuleId,
          userService: this.props.userService,
        });

        if (selectedKeys?.length && !isSkipCache) {
          keys = selectedKeys.map(String);
        } else {
          keys = ['all'];
        }

        const expandNodes = expandKeys;
        this.setState({
          expandNodes: [expandNodes[0]],
          categoryTree,
          selectedKeys: keys,
        });
      });
    };

    /** 初始化进入页面,弹窗选择门店,点击确定之后的操作 */
    onOkInGlobalModalPoiSelector = (value: Org) => {
      const prrUrl = source === WaimaiSourceEnum.MEITUAN ? '/web/operation/goods/waimailist' : '/web/operation/goods/takeaway-eleme';
      let urlPath = this.isSingle && checkVirtual() ? '/rms-goods/waimai/list' : '/rms-goods/waimai/list-manage';
      const curIsVirtual = isVirtualPoi(value);
      const orgId = getOrgId(value);
      historyPush(
        this.props.history,
        `${prrUrl}#${urlPath}/${source}?managePoi=true&poiId=${getMainPoiIdByOrg(value)}&orgId=${orgId}${
          curIsVirtual ? `&virtualPoiId=${value.poiId}` : ''
        }${curIsVirtual ? `&relationPoiId=${value.mainPoiId}` : ''}`,
        true,
        {
          _option: {
            navigatorType: NAVIGATORTYPE.NORMAL,
          },
        }
      );
    };

    onModalClose = (refreshTable?: boolean, refreshCategory?: boolean) => {
      this.setState({ batchModalShow: false });
      refreshTable && this.refreshAll(refreshCategory);
    };

    showPoiSelector = () => {
      this.setState({ poiSelectorVisible: true });
    };

    closePoiSelector = () => {
      this.setState({ poiSelectorVisible: false });
    };

    onRel = (spu: WmRecord) => {
      this.setState({ selectedSpu: spu });
      this.showPoiSelector();
    };

    onSelectPoi = (pois: Org[]) => {
      const { id, type, comboVersion } = this.state.selectedSpu || {};
      if (!id || type === undefined) return;
      historyPush(this.props.history, `/web/operation/chain/waimai#/rms-goods/waimai/poi-rel/${source}/${type}/${id}`, true, {
        pois,
        comboVersion,
      });
      this.setState({ poiSelectorVisible: false });
    };

    // 报表列配置
    getColumns = (options: Pick<IOptions, 'abStrategyMap' | 'switchStatusMap' | 'taxRateSwitchStatus'>) => {
      const columns = getWaimaiListColumns({
        source,
        isManagePoi,
        isChainPage,
        history: this.props.history,
        refresh: this.refreshAll,
        // hasPermission,
        analytics: this.analytics,
        model: this.model,
        isTangshiPage: this.props.isTangshi,
        onLoading: (loading?: boolean) => {
          if (loading) {
            this.model.syncStatus = SyncStatusEnum.PENDING;
          } else {
            this.model.syncStatus = SyncStatusEnum.SUCCESS;
          }
        },
        onRelBtnClick: this.onRel,
        // 设置模板
        onSetTemplate: (record) => {
          this.operateRecord = record;
          this.setState({ showBatchSetTemplate: true });
        },
        // 味千灰度
        abStrategyMap: options.abStrategyMap,
        switchStatusMap: options.switchStatusMap,
        taxRateSwitchStatus: options.taxRateSwitchStatus,
        formRef: this.formRef,
      });
      return columns;
    };

    /**
     * updateModel重新初始化一个model对象, 在WaimaiService里面将poiId和orgId设置到了网络请求库的请求头上
     * 在切换门店onChange和回到第一个Tab（onTabChange）的时候需要重新查询分类和列表，重新new一个model，将当前新的poiId和orgId更新
     * @param oldStatusMap
     * 参数oldStatusMap是一个可选项，用于保存更新model之前的相关状态，在onChangePoi的时候不需要传，因为会去调用同步菜的方法，会重新设置syncStatus的状态，
     * 在onTabChange的时候，是没有同步菜的操作，所以导致syncStatus被设为初始值，所以在更新model之后，将原来保存的oldStatusMap赋值给新的model
     */
    updateModel = (oldStatusMap?: IV) => {
      this.model = new WaimaiModel(source, isManagePoi || (!isChainPage && !this.props.isTangshi && checkVirtual()), isChainPage, this.org);
      if (oldStatusMap) {
        this.model.syncStatus = oldStatusMap.syncStatus;
      }
    };

    /** 列表页查询条件切换门店 */
    onChangePoi = async (value: Org) => {
      onPoiSelectChange(value, this.poiRef);
      this.updateModel();
      this.getCategoryTree(true);
      // 切换门店后重新同步外卖菜品数据
      await this.model.syncStatusAction();
      // 切换门店后重新获取开关状态
      const { switchStatusMap, taxRateSwitchStatus } = await this.getSwitchStatus();
      // 开关状态更新，重新初始化字段设置
      this.onFieldsSetting(true, {
        switchStatusMap: switchStatusMap ?? void 0,
        taxRateSwitchStatus,
        abStrategyMap: this.state.abStrategyMap,
      });
      // 更新接单非接单的状态
      this.isUseSide = await getMTSideIsUse(!!isChainPage, isChainPage ? undefined : usedPoiId());

      // 状态同步成功并且TableRef存在之后再去刷新
      this.setState({ selectedKeys: ['all'] });
      this.treeReportRef?.setQuery({ pageNo: 1 });
    };

    // 不要抽离出去，否则报表组件会有更新问题
    // 报表查询配置
    getQuery = (wmSource: WaimaiSourceEnum, { query, setQuery }: { query: IV; setQuery: (args: IV, query?: boolean) => void }): IV => {
      return {
        org: {
          label: '门店',
          content: (
            <PoiSelector
              customFilter={{
                businessModuleId: BusinessModule.菜品,
                filterWmPoiPurchase: source === WaimaiSourceEnum.MEITUAN ? WmPoiPurchase.MT : WmPoiPurchase.ELE,
                ...baseCostomFilter,
                // 展示外卖门店ID
                showShopId: isManagePoi,
              }}
              multiple={false}
              allowClear={false}
              onChange={this.onChangePoi}
              useEntity
              source={source}
              isVirtual={checkVirtual()}
            />
          ),
          defaultValue: usedPoiId(),
          hide: !isManagePoiUtil(),
        },
        spuName: {
          label: '菜品名称',
          content: <Input placeholder='请输入' />,
          defaultValue: '',
        },
        numMnemonicCode: {
          label: '助记码',
          content: <Input placeholder='请输入' />,
          defaultValue: '',
          hide: !this.state.abStrategyMap?.[ABtestEnum.味千],
        },
        spuTypes: {
          label: '菜品类型',
          content: (
            <Select
              onChange={(v) => {
                const queryParams = this.treeReportRef?.getQuery();
                if (v === SpuTypeEnum.SIDE) {
                  setQuery({ isNotSingle: null, spuTypes: v });
                  message.warn(`${getSideName(wmSource === WaimaiSourceEnum.MEITUAN)}没有单点不送配置项`);
                } else if (queryParams?.isNotSingle === null) {
                  setQuery({ isNotSingle: IsNotSingleEnum.ALL, spuTypes: v }, false);
                }
              }}
            >
              <Option value={SpuTypeEnum.ALL}>全部菜品</Option>
              <Option value={SpuTypeEnum.GOODS}>普通菜</Option>
              {/* 饿了么新套餐ab内容 */}
              {(wmSource === WaimaiSourceEnum.MEITUAN || this.state?.abStrategyMap?.[ABtestEnum.饿了么新套餐]) && (
                <Option value={SpuTypeEnum.COMBO}>套餐</Option>
              )}
              {<Option value={SpuParseType.selectValue}>{SpuParseType.CASHIERCOMBO_TEXT}</Option>}
              <Option value={SpuTypeEnum.SIDE}>{getSideName(wmSource === WaimaiSourceEnum.MEITUAN)}</Option>
            </Select>
          ),
          defaultValue: SpuTypeEnum.ALL,
        },
        isNotSingle: {
          label: '单点不送',
          content: (
            <Select disabled={query.spuTypes === SpuTypeEnum.SIDE}>
              <Option value={IsNotSingleEnum.ALL}>全部</Option>
              <Option value={IsNotSingleEnum.YES}>是</Option>
              <Option value={IsNotSingleEnum.NO}>否</Option>
            </Select>
          ),
          defaultValue: IsNotSingleEnum.ALL,
        },
      };
    };

    // 是否显示堂食外卖菜品关联页
    getIsRenderGoodsList = () => {
      // 门店视角 或者穿透视角展示 或者从外卖属性未关联页跳过来.
      return (
        (this.isSingle || !!this.showWmTabs || Boolean(new URLSearchParams(this.props.location.search).get('isAssociatedPage'))) &&
        this.state.switchStatusMap?.[SWITCH_KEY_ENUM.使用堂食外卖关联]
      );
    };

    reportRefresh = () => {
      this.treeReportRef?.refresh();
    };

    refreshAll = (refreshCategory?: boolean) => {
      this.reportRefresh();
      refreshCategory && this.getCategoryTree();
    };

    renderError = () => {
      const doAgain = (
        <Button
          onClick={async () => {
            // 重新同步需要重新去判断当前的美团商户的接单状态
            this.isUseSide = await getMTSideIsUse(!!isChainPage, isChainPage ? undefined : usedPoiId());
            await this.model.syncStatusAction();
          }}
        >
          重新同步
        </Button>
      );
      return <Result status='error' title='同步失败' subTitle='外卖菜同步失败，请刷新页面或者重新同步' extra={doAgain} />;
    };

    onActionSelect = async (batchConfig: BatchConfigProps) => {
      // 在外卖菜品关联堂食菜品多对一白名单中，需要禁用堂食导外卖
      if (
        this.state.abStrategyMap?.[ABtestEnum.外卖菜品关联堂食菜品多对一] &&
        (batchConfig.routeEnum === 'CHAINMPORT' || batchConfig.routeEnum === 'IMPORT')
      ) {
        return message.warn(`当前${isChainPage ? '集团' : '门店'}堂食菜品可关联多个外卖菜品，无法支持堂食菜品导入外卖。`);
      }
      //  打印分类菜品排序
      if (batchConfig.routeEnum === 'CATEGORY_PRINT_SORT') {
        this.setState({
          showWaimaiGoodsSelector: true,
        });
        return;
      }
      batchConfig.lxbid && this.analytics.moduleClick(batchConfig.lxbid);
      this.onActionClick(batchConfig);
      if (batchConfig.beforeActions) {
        const res = await batchConfig.beforeActions();
        if (!res) return;
      }
      if (!batchConfig.route && !batchConfig.query) {
        batchConfig.show = true;
        this.BatchModal = batchConfig.component as React.ComponentClass<BatchModalProps>;
        this.setState({ batchModalShow: true });
      } else if (batchConfig.route) {
        // URL 参数去重
        const query = getUniqueUrlParams(this.props.location.search, batchConfig.query);
        batchConfig && historyPush(this.props.history, `/waimai/batch${batchConfig.route}${query}`);
      } else if (batchConfig.crossTabMap) {
        // 跨页签跳转页面
        const { crossTab, historyLocationState } = batchConfig.crossTabMap;
        batchConfig && historyPushV2(this.props.history, `${batchConfig.query}`, crossTab, historyLocationState);
      } else {
        batchConfig && historyPush(this.props.history, `${batchConfig.query}`);
      }
    };

    //事件埋点
    onActionClick = (batchConfig: BatchConfigProps) => {
      const { routeEnum } = batchConfig;
      let bid = '';
      if (routeEnum === 'SHELF') {
        const options = {
          Bid: 'b_eco_8moyb22o_mc',
          manageBid: 'b_eco_p8qt444u_mc',
          chainBid: 'b_eco_vdgippge_mc',
        };
        const aBid = getBidByOption(options, source, isManagePoi, isChainPage);
        this.analytics.moduleClick(aBid);
      } else if (routeEnum === 'CHAINCREATE') {
        bid = 'b_eco_p1wpj7yw_mc';
      } else if (routeEnum === 'PUBLISH') {
        bid = 'b_eco_h0ui97h1_mc';
      } else if (routeEnum === 'CHAINMPORT' || routeEnum === 'IMPORT') {
        bid = 'b_eco_kobgr93d_mc';
      } else if (routeEnum === 'COPY') {
        bid = 'b_eco_3x26ish1_mc';
      } else if (routeEnum === 'IMPORTRULES') {
        // 导入&复制说明
        bid = 'b_eco_g53h9l2f_mc';
      } else if (routeEnum === 'EXPORT') {
        // 菜品导出
        bid = 'b_eco_8ivgj6rt_mc';
      } else if (routeEnum === 'SORT') {
        // 菜品排序
        bid = 'b_eco_fxf12hfa_mc';
      } else if (routeEnum === 'PRINT_SORT') {
        // 打印排序
        bid = 'b_eco_o7ldpd9r_mc';
      }
      !!bid && this.analytics.moduleClick(bid);
    };

    renderBatchButtons = () => {
      const batchConfigs = batchWaimaiConfig(source, {
        isManagePoi,
        isChainPage,
        isTangshi: this.props.isTangshi,
        hiddenMap: { ...this.state.switchStatusMap },
      });
      let filterdBatchConfigs: BatchConfigProps[] = batchConfigs.filter((config) => {
        // 如果hidden为true，则隐藏
        if (config.hide) {
          return false;
        }
        if (config.name === '批量设置税目' && this.model.taxRateSwitchStatus === TaxRateSwitchStatusEnum.OFF) {
          return false;
        }
        if (config.name === '批量选择打印配置' && !isChainPage) {
          return false;
        }
        if (config.name === BatchActionRouteEnum.SIGNATUREDISH && !isChainPage) {
          return false;
        }
        // 非整包不展示批量设置模板
        if (config.name === BatchActionRouteEnum.BATCH_SET_TEMPLATE && !this.state.abStrategyMap?.[ABtestEnum.外卖整包]) {
          return false;
        }
        return checkhasPermissionSync(config.permissionCode);
      });
      return (
        <>
          {filterdBatchConfigs.length > 0 && (
            <Select
              dropdownMatchSelectWidth={false}
              value='更多批量操作'
              className={styles['batch-modal-select']}
              onFocus={() => {
                this.analytics.moduleClick('b_eco_yajq5e3b_mc');
              }}
              onSelect={(value: string) => {
                const activedConfig = filterdBatchConfigs.find(({ routeEnum }) => routeEnum === value);
                if (!activedConfig) return;
                this.onActionSelect(activedConfig);
              }}
            >
              {filterdBatchConfigs.map((config) => (
                <Option key={config.name} value={config.routeEnum}>
                  {config.name}
                </Option>
              ))}
            </Select>
          )}
        </>
      );
    };

    onPinCanBtn = (baseUrl: string) => {
      const virtualPoiIdSearchStr = poiConfig.virtualPoiId ? `&virtualPoiId=${poiConfig.virtualPoiId}` : '';
      const relationPoiId = getRelationPoiId();
      const relationIdSearchStr = relationPoiId ? `&relationPoiId=${relationPoiId}` : '';

      if (!!isManagePoi || checkVirtual()) {
        historyPush(
          this.props.history,
          `${baseUrl}?managePoi=true&poiId=${poiConfig.poiId}&orgId=${poiConfig.orgId}${virtualPoiIdSearchStr}${relationIdSearchStr}`
        );
        return;
      }
      historyPush(this.props.history, baseUrl);
    };

    renderPinCanEntry = () => {
      const isUserAllowed = !!isManagePoi || !isChainPage || !!this.props.userService.isSinglePoi();
      if (!isUserAllowed) return null;

      const { switchStatusMap } = this.state;
      const isSwitchOn = (key: SWITCH_KEY_ENUM) => !!switchStatusMap?.[key];

      if (source === WaimaiSourceEnum.MEITUAN && isSwitchOn(SWITCH_KEY_ENUM.拼好饭菜品管理)) {
        return this.renderLink('美团拼好饭菜品管理', '/waimai/pinhaofan/manager', () => {
          moduleClick('b_eco_lzf1s0qp_mc', {}, { cid: 'c_eco_07o1dwa4' });
        });
      }

      if (source === WaimaiSourceEnum.ELEME && isSwitchOn(SWITCH_KEY_ENUM.拼团菜品管理)) {
        const { bid, cid } = getAnalyticsCode(AnalyticsEnum.GoodsManageBtnMC, WaimaiSourceEnum.PT);
        return this.renderLink('拼团菜品管理', '/waimai/pintuan/manager', () => {
          moduleClick(bid, {}, { cid });
        });
      }
      return null;
    };

    renderLink = (text: string, url: string, moduleClick: Function) => (
      <a
        style={{
          marginRight: 16,
        }}
        onClick={() => {
          moduleClick();
          this.onPinCanBtn(url);
        }}
      >
        {text}
      </a>
    );

    renderSideEntry = (permissionCode: WaimaiPermissionCode) => {
      // 美团外卖如果没有加料能力直接不展示
      if (this.isMT && !this.isUseSide) return null;
      return (
        <Permission code={[permissionCode]}>
          <a
            id={`${AutomationIdEnum.外卖菜列表}${FunctionIdEnum.加料管理}`}
            style={{
              marginRight: 16,
            }}
            onClick={() => {
              const params = new URLSearchParams({
                ...this.props.matchQuery,
                wmSource: String(source),
              });
              historyPush(this.props.history, `/rms-goods/waimai/side-manage?${params}`);
            }}
          >
            {this.isMT ? '小料库管理' : '加料管理'}
          </a>
        </Permission>
      );
    };

    renderExtra = () => {
      if (isChainPage) {
        const chainPermissionCode = this.isMT ? WaimaiPermissionCode.MT_CHAIN_SIDE_MANAGE : WaimaiPermissionCode.CHAIN_SIDE_MANAGE;
        return (
          <div>
            {this.renderSideEntry(chainPermissionCode)}
            <Permission code={[WaimaiPermissionCode.CHAIN_GOODS_HISTORY]}>
              <a
                className='publish-history'
                onClick={() => {
                  this.analytics.moduleClick('b_eco_vo88hvpk_mc');
                  let url = `/web/operation/chain/waimai#/rms-goods/waimai/publish/history?wmSource=${source}&isChainPage=1`;
                  if (this.state.abStrategyMap?.[ABtestEnum.外卖整包]) {
                    url = '/web/operation/waimai/publish#/rms-goods/waimai/pkg-pub/1';
                  }
                  historyPushV2(this.props.history, url, true, {
                    activeTabKey: PkgPubTabsKey.下发历史,
                  });
                }}
              >
                菜品发布历史
              </a>
            </Permission>
          </div>
        );
      }

      let permissionCode;
      if (this.isMT) {
        permissionCode = isManagePoi ? WaimaiPermissionCode.MT_MANAGEPOI_SIDE_MANAGE : WaimaiPermissionCode.MT_POI_SIDE_MANAGE;
      } else {
        // 饿了么code
        permissionCode = isManagePoi ? WaimaiPermissionCode.MANAGEPOI_SIDE_MANAGE : WaimaiPermissionCode.POI_SIDE_MANAGE;
      }
      return (
        <div>
          {this.renderSideEntry(permissionCode)}
          {this.renderPinCanEntry()}
          <a
            onClick={() => {
              const m = showModal({
                title: '外卖菜品注意事项',
                children: (
                  <ol>
                    {TIP_TEXT(source).map((text) => (
                      <li key={text as string}>{text}</li>
                    ))}
                  </ol>
                ),
                footer: (
                  <Button
                    type='primary'
                    onClick={() => {
                      m.destroy();
                    }}
                  >
                    知道了
                  </Button>
                ),
              });
              this.analytics.moduleClick('b_eco_qswzjig8_mc');
            }}
          >
            外卖菜品注意事项
          </a>
        </div>
      );
    };

    renderSortGoods = () => {
      if (!isChainPage) return;
      return (
        <Permission code={WaimaiPermissionCode.CHAIN_GOODS_SORT}>
          <Button
            id={`${AutomationIdEnum.外卖菜列表}${FunctionIdEnum.菜品排序}`}
            onClick={() => {
              this.analytics.moduleClick('b_eco_fxf12hfa_mc');
              const refresh = this.reportRefresh;
              const serviceProps = { source, isManagePoi, isChainPage, operatedPoi: this.org };
              GoodSort({
                refresh,
                wmSource: source,
                serviceProps,
                showSideConfig: {
                  showSide: CommonConfirmEnum.NO,
                },
              });
            }}
          >
            菜品排序
          </Button>
        </Permission>
      );
    };

    renderPrintSort = () => {
      if (!isChainPage || !this.state.switchStatusMap?.[SWITCH_KEY_ENUM.CHAIN_WAIMAI_PRINT_SORT]) return;
      return (
        <Permission code={WaimaiPermissionCode.CHAIN_CATEGORY_EDIT}>
          <Button
            id={`${AutomationIdEnum.外卖菜列表}${FunctionIdEnum.打印排序}`}
            onClick={() => {
              this.setState({
                showWaimaiGoodsSelector: true,
              });
              this.analytics.moduleClick('b_eco_o7ldpd9r_mc');
            }}
          >
            打印排序
          </Button>
        </Permission>
      );
    };

    renderCommonButton = (config: BatchConfigProps) => {
      if (config.permissionCode.length) {
        return (
          <Permission code={config.permissionCode}>
            <Button
              id={
                config.name === '新建菜品'
                  ? `${AutomationIdEnum.外卖菜列表}${FunctionIdEnum.新建菜品}`
                  : config.name === '新建套餐'
                  ? `${AutomationIdEnum.外卖菜列表}${FunctionIdEnum.新建套餐}`
                  : undefined
              }
              type={config.type as 'primary'}
              onClick={() => this.onActionSelect(config)}
            >
              {config.name}
            </Button>
          </Permission>
        );
      } else {
        return (
          <Button type={config.type as 'primary'} onClick={() => this.onActionSelect(config)}>
            {config.name}
          </Button>
        );
      }
    };

    renderABButton = ({
      abConfig,
      ...config
    }: Omit<BatchConfigProps, 'abConfig'> & {
      abConfig: { key: string; b: BatchConfigProps };
    }) => <AB abKey={abConfig.key} a={() => this.renderCommonButton(config)} b={() => this.renderCommonButton(abConfig.b)} />;

    renderComboCreateBtn = () => {
      const { isElemeNewTenant, switchStatusMap, abStrategyMap } = this.state;
      const isElemNewComboAB = abStrategyMap?.[ABtestEnum.饿了么新套餐];
      const cashierComboSwitchStatus = switchStatusMap?.[SWITCH_KEY_ENUM.美团收银套餐];
      const permissionCode = getWaimaiComboPermissionCode(importPermissionOptions, source, isManagePoi);
      const isShow = isShowComboCreateBtn({ isElemeNewTenant, isElemNewComboAB, source, cashierComboSwitchStatus });
      const isElmeNewCombo = isElemNewComboAB && source === WaimaiSourceEnum.ELEME;
      const baseUrl = isElmeNewCombo ? '/waimai/elem-new-combo-create/' : '/waimai/combo-create/';
      return (
        isShow && (
          <Permission code={permissionCode}>
            <Button
              id={`${AutomationIdEnum.外卖菜列表}${FunctionIdEnum.新建套餐}`}
              type='primary'
              onClick={async () => {
                this.analytics.moduleClick('b_eco_h0ui97h1_mc');
                historyPathnamePush(this.props.history, `${baseUrl}${source}`, { isTangshi: this.props.isTangshi });
              }}
            >
              新建套餐
            </Button>
          </Permission>
        )
      );
    };

    renderButtonGroup = () => {
      return (
        <div className={styles['action-container']}>
          <div className={styles['action-btn-group']}>
            {/* 集团页面的button */}
            {isChainPage ? (
              chainWaimaiConfig(source, this.state.switchStatusMap, this.state.isElemeNewTenant, this.state.abStrategyMap).map(
                (config: BatchConfigProps) => {
                  return config.abConfig ? this.renderABButton(config as Required<BatchConfigProps>) : this.renderCommonButton(config);
                }
              )
            ) : (
              // 非集团页面的button
              <>
                <Permission code={getWaimaiPermissionCode(importPermissionOptions, source, isManagePoi)}>
                  <Button
                    id={`${AutomationIdEnum.外卖菜列表}${FunctionIdEnum.新建菜品}`}
                    type='primary'
                    onClick={() => {
                      this.analytics.moduleClick('b_eco_p1wpj7yw_mc');
                      historyPathnamePush(this.props.history, `/waimai/create/${source}`, { isTangshi: this.props.isTangshi });
                    }}
                  >
                    新建菜品
                  </Button>
                </Permission>
                {this.renderComboCreateBtn()}
                {/* hiddenImportPoiDishes虚拟门店不展示导入店内菜品按钮 */}
                {poiWaimaiConfig(source, {
                  isManagePoi,
                  isTangshi: this.props.isTangshi,
                  hiddenMap: { ...this.state.switchStatusMap, hiddenImportPoiDishes: isVirtualPage() },
                  showSideConfig: { showSide: CommonConfirmEnum.NO },
                }).map((config) =>
                  !config.hidden ? (
                    <Permission code={config.permissionCode}>
                      <Button
                        type={(config.type as unknown) as 'default'}
                        onClick={() => {
                          this.onActionSelect(config);
                        }}
                      >
                        {config.name}
                      </Button>
                    </Permission>
                  ) : null
                )}
              </>
            )}
            {this.renderBatchButtons()}
            {this.renderSortGoods()}
            {this.renderPrintSort()}
          </div>
          {this.renderExtra()}
        </div>
      );
    };

    renderGoodsList = () => {
      const { categoryTree, selectedKeys } = this.state;

      const pagedAdapter = async ({
        spuName,
        numMnemonicCode,
        categoryId,
        pageSize,
        pageNo,
        spuTypes,
        isNotSingle,
      }: IQuery & ExtraInfo) => {
        const brandParam = isChainPage ? { brandId: '' } : {};
        const isNotSingleParam = isNotSingle && isNotSingle < IsNotSingleEnum.ALL ? isNotSingle : '';
        // 处理菜品类型
        let comboVersions;
        /** 饿了么新套餐ab内容 */
        if (spuTypes === SpuTypeEnum.COMBO) {
          const isNewEleComboABExistingAndEle = this.state?.abStrategyMap?.[ABtestEnum.饿了么新套餐] && source === WaimaiSourceEnum.ELEME;
          comboVersions = isNewEleComboABExistingAndEle ? SpuTypes.饿了么套餐 : SpuTypes.美团套餐;
        }
        if (spuTypes === SpuParseType.selectValue) {
          comboVersions = SpuTypes.收银套餐;
          spuTypes = SpuTypeEnum.COMBO;
        }
        const param = {
          spuName,
          categoryId: categoryId || undefined,
          ...brandParam, // 集团新增品牌入参
          pageNo,
          pageSize,
          wmSource: source,
          includeCombo: 1, // 包含套餐，直接写死
          spuTypes: JSON.stringify([spuTypes]),
          isNotSingle: isNotSingleParam,
          filterSaleSku: true, // 51110新增入参：https://km.sankuai.com/page/373623890
          needMnemonicCode: this.state.abStrategyMap?.[ABtestEnum.味千], // 52810新增入参：仅对外卖列表页需要返回助记码的商家（WQ），设置为true。非WQ传false或不传
          numMnemonicCode, // 52810新增助记码查询字段
          comboVersions,
          isOpenParseCombo: !!this.state.switchStatusMap?.[SWITCH_KEY_ENUM.美团收银套餐] || undefined,
        };
        const treeKeys = (selectedKeys[0] || '').split(listStandard.JOIN_KEY);

        if (isChainPage) {
          param.brandId = treeKeys[0] === 'all' ? '' : treeKeys[0];
          if (treeKeys && treeKeys.length > 1) {
            param.categoryId = treeKeys[1];
          }
        } else {
          param.categoryId = treeKeys[treeKeys.length - 1];
        }

        if (param.categoryId === 'all') {
          param.categoryId = '';
        }
        if (!isChainPage && checkVirtual()) {
          // @ts-ignore
          param.relationPoiId = getRelationPoiId();
        }

        // 存储菜品导出所需参数(因518后端无法支持筛选，暂只传wmSource)
        this.model.queryfiled = { wmSource: `${source}`, publishTypes: [PublishType.Waimai] };
        if (!!getVirtualPoiId()) {
          // 导出需要增加virtualPoiId参数
          this.model.queryfiled = { ...this.model.queryfiled, virtualPoiId: getVirtualPoiId() };
        }
        this.model.queryInfo = getWmQueryInfo(param);
        // todo 或者list
        const startTime = Date.now();
        const reportChannel = source === WaimaiSourceEnum.MEITUAN ? '美团外卖菜品' : '饿了么外卖菜品';
        const apiType = isChainPage ? 'chain' : 'poi';
        const reportNetUrl = `/api/v2/goods/${apiType}/wmgoods/list-page`;
        return fetchWaimaiSpuList2(noEmpty(param), !!isManagePoiUtil(), isChainPage, this.org)
          .then((res) => {
            reportNetSuccess({
              API: reportNetUrl,
              CHANNEL: reportChannel,
            });
            if (res && res.items) {
              const list = formUtil.priceParse(
                listUtil.waimaiSpuParse(res.items || [], (spu) => {
                  spu.printConfigs = (spu.printConfigNameMap && spu.printConfigNameMap[spu.id]) || {
                    configName: '--',
                  };
                  return spu;
                  // 组件库类型修复
                  /* eslint-disable-next-line */
                }) as any[],
                PriceParseTypeEnum.fen2yuan
              );
              if (this.isFirst) {
                const bid = source === WaimaiSourceEnum.MEITUAN ? 'b_eco_fbp9w15q_mc' : 'b_eco_axmpgh9j_mc';
                const cid = source === WaimaiSourceEnum.MEITUAN ? 'c_eco_grppeh3p' : 'c_eco_8hqj4m5r';
                moduleClick(bid, {}, { cid });
                this.isFirst = false;
              }

              return {
                list,
                totalCount: Number(res.totalCount),
              };
            }

            return { list: [], totalCount: 0 };
          })
          .catch((e) => {
            console.error(e);
            let text = (e && (e.message || e.msg)) || '网络连接错误，稍候重试';
            if (/^[a-zA-Z0-9\s.,/\\&=#:()',-^`]+$/.test(text)) {
              console.warn('[Net Error]', text);
              text = '网络连接错误，稍候重试';
            }
            reportNetErr({
              API: reportNetUrl,
              CHANNEL: reportChannel,
            });
            throw new Error(text);
          })
          .finally(() => {
            const costTime = Date.now() - startTime;
            reportNetTime(costTime, {
              API: reportNetUrl,
              CHANNEL: reportChannel,
            });
            reportNetCount({
              API: reportNetUrl,
              CHANNEL: reportChannel,
            });
          });
      };

      return (
        <>
          {this.renderButtonGroup()}
          {categoryTree && categoryTree.length > 0 ? (
            <TreeTableReport<IQuery, WmRecord>
              refCore={(r) => {
                this.treeReportRef = r;
              }}
              standardProps={{
                persistenceModuleId: this.persistenceModuleId,
                indexRowMergeKey: 'wmSpuId',
                showLoadingIfNoColumns: true,
                clearQueryIfNotTheSamePoi: {
                  orgKey: 'org',
                },
                rowHasChildren: true,
              }}
              tableProps={{
                unlimitedPageSize: true,
                columns: listStandard.fieldsSettingUtils.getColumns(this.state.allColumns),
                rowKey: (row) => {
                  // 如果关联了堂食套餐，子菜的wmSku 可能重复
                  const { wmSkuId, relationWmSkuId } = row;
                  return relationWmSkuId ? `${wmSkuId}_${relationWmSkuId}` : wmSkuId;
                },
                components: this.isEditPermission
                  ? {
                      body: {
                        row: EditableCell.EditableFormRow,
                        cell: EditableCell.EditableCellSaas,
                      },
                    }
                  : {},
              }}
              maxQueryCount={5}
              renderQuery={(query: IQuery, _: WmRecord[], setQuery: (args: IV) => void) => this.getQuery(source, { query, setQuery })}
              treeProps={{
                tree: categoryTree,
                joinKeys: listStandard.JOIN_KEY,
                onSelect: (treeParams) => {
                  const selectedKeys = treeParams?.map(String);
                  WmSpuNameModel.setSelectKeys(selectedKeys);
                  this.setState({
                    selectedKeys,
                  });
                },
                onExpand: (expandNodes) => this.setState({ expandNodes: expandNodes?.map(String) }),
                expandedKeys: this.state.expandNodes,
                selectedKeys: this.state.selectedKeys,
              }}
              pageAdapter={pagedAdapter}
              resetButtonProps={{
                onClick: () => {
                  this.treeReportRef?.reset();
                  if (!!isManagePoiUtil()) {
                    this.treeReportRef?.setQuery({
                      org: usedPoiId(),
                    });
                  }
                  this.setState({
                    selectedKeys: ['all'],
                  });
                },
              }}
            />
          ) : null}

          <PoiModalSelector
            visible={this.state.poiSelectorVisible}
            multiple
            wmSource={(source as unknown) as WmSource}
            onChange={(v: Org[]) => {
              this.onSelectPoi(v);
            }}
            onCancel={() => this.closePoiSelector()}
            customFilter={{
              specificBrands: !!this.state?.selectedSpu?.brandId ? [this.state?.selectedSpu?.brandId] : [],
              ...baseCostomFilter,
            }}
            isVirtual={checkVirtual()}
          />
          <WmPrintSort
            visible={this.state.showWaimaiGoodsSelector}
            isChainPage={isChainPage}
            wmSource={source}
            onCancel={() => {
              this.setState({
                showWaimaiGoodsSelector: !this.state.showWaimaiGoodsSelector,
              });
            }}
          />
          <BatchSetTemplate
            title='设置模板'
            wmSource={source}
            show={this.state.showBatchSetTemplate}
            onClose={() => this.setState({ showBatchSetTemplate: false })}
            goodsInfo={this.operateRecord}
          />
        </>
      );
    };

    /** 门店美团外卖菜品 */
    renderWmGoodsList = () => {
      const { syncStatus, isSyncStatusActionCalled } = this.model;

      if ((isManagePoi || this.isSingleHasVirNotTangshi) && (!usedPoiId() || !this.state.isCurrentRoute)) {
        return <SelectPoiModal isVirtual={checkVirtual()} source={source} onOk={this.onOkInGlobalModalPoiSelector} showShopId={true} />;
      }

      const BatchActionModal = this.BatchModal;

      if (this.isSingle && !this.state.buyStatus) {
        return (
          <div id='waimai-goods-list-container' className='waimai-goods-list-container'>
            <div className='wm-sync-status'>
              <Button type='primary' onClick={async () => await this.initStatus()}>
                重新校验购买状态
              </Button>
            </div>
          </div>
        );
      }

      return (
        <div id='waimai-goods-list-container' className='waimai-goods-list-container'>
          {syncStatus === SyncStatusEnum.SUCCESS || syncStatus === SyncStatusEnum.PENDING ? this.renderGoodsList() : null}
          {syncStatus === SyncStatusEnum.NONE ? (
            <Spin spinning={!isSyncStatusActionCalled}>
              <div className='wm-sync-status'>
                <Button type='primary' onClick={async () => await this.ensureWmStatus()}>
                  重新校验登录状态
                </Button>
              </div>
            </Spin>
          ) : null}
          {/* 无需路由跳转的批量操作弹窗，如批量删除 */}
          {BatchActionModal && (
            <BatchActionModal
              wmSource={source}
              isManagePoi={isManagePoi}
              isChainPage={isChainPage}
              model={this.model}
              reportRefresh={this.reportRefresh}
              onClose={this.onModalClose}
              show={this.state.batchModalShow}
              limitSheetsNoEmpty
              userService={this.props.userService}
            />
          )}
        </div>
      );
    };

    /** 堂食外卖菜品关联 */
    renderWmBind = () => {
      const { syncStatus, isSyncStatusActionCalled } = this.model;
      // @ts-ignore
      const tabKey: WmTabPanelEnum = source === WaimaiSourceEnum.MEITUAN ? TsTabPanelEnum.美团外卖关联 : TsTabPanelEnum.饿了么外卖关联;
      if (this.isSingle && !this.state.buyStatus) {
        return (
          <div id='waimai-goods-list-container' className='waimai-goods-list-container'>
            <div className='wm-sync-status'>
              <Button type='primary' onClick={async () => await this.initStatus()}>
                重新校验购买状态
              </Button>
            </div>
          </div>
        );
      }
      if (syncStatus === SyncStatusEnum.SUCCESS || syncStatus === SyncStatusEnum.PENDING) {
        return source === WaimaiSourceEnum.MEITUAN ? (
          <BindMtGoodsListManyForOne
            {...this.props}
            // 可用的外卖平台(已购买+已绑定), 能render表示已经通过了前面的购买判定
            wmStatus={WmStatus.MT}
            wmSource={WaimaiSourceEnum.MEITUAN}
            isWmPage
            visible={this.state.activeKey === tabKey}
            // 页面引入地方较多  将hasVirtual参数收口到此处传递
            hasVirtual={checkVirtual()}
            isSingle={this.isSingle}
            loadWmDishes={this.model.syncStatusAction}
          />
        ) : (
          <BindElmGoodsListManyForOne
            {...this.props}
            wmStatus={WmStatus.ELE}
            wmSource={WaimaiSourceEnum.ELEME}
            isWmPage
            visible={this.state.activeKey === tabKey}
            hasVirtual={checkVirtual()}
            isSingle={this.isSingle}
            loadWmDishes={this.model.syncStatusAction}
          />
        );
      }
      if (syncStatus === SyncStatusEnum.NONE) {
        return (
          <div id='waimai-goods-list-container' className='waimai-goods-list-container'>
            <Spin spinning={!isSyncStatusActionCalled}>
              <div className='wm-sync-status'>
                <Button type='primary' onClick={async () => await this.ensureWmStatus()}>
                  重新校验登录状态
                </Button>
              </div>
            </Spin>
          </div>
        );
      }
      return null;
    };

    /** 门店与集团外卖菜关联 */
    renderBindWmChainList = () => {
      const bindWmChainListProps = { isChainPage, isManagePoi, source };
      // showWmTabs: 是否展示门店关联集团菜tab, 仅穿透视角进行tab展示
      if (this.showWmTabs && this.state.switchStatusMap?.[SWITCH_KEY_ENUM.使用集团外卖关联]) {
        return (
          <TabPane
            tab='门店与集团外卖菜关联'
            menuCode={source === WaimaiSourceEnum.MEITUAN ? 2151 : 2152}
            key={WmTabPanelEnum.BIND_CHAIN_GOODS_LIST}
          >
            {this.renderTabPane(
              <BindWmChainList
                {...this.props}
                {...bindWmChainListProps}
                visible={this.state.activeKey === WmTabPanelEnum.BIND_CHAIN_GOODS_LIST}
                loadWmDishes={this.model.syncStatusAction}
              />
            )}
          </TabPane>
        );
      }
      return null;
    };

    /**
     * 门店与集团外卖属性关联
     */
    renderAttrBind = () => {
      const wmSource = source === WaimaiSourceEnum.MEITUAN ? WaimaiSourceEnum.MEITUAN : WaimaiSourceEnum.ELEME;
      return (
        <BindChainAttrList
          wmSource={wmSource}
          isManagePoi={isManagePoi || false}
          // @ts-ignore
          refCall={(v) => (this.bindChainAttrListTabRef.current = v)}
          loadWmDishes={this.model.syncStatusAction}
          model={this.model}
          renderError={this.renderError}
        />
      );
    };

    renderPublishHistory = () => {
      const goPublish = () => {
        if (isManagePoi && source === WaimaiSourceEnum.ELEME) {
          moduleClick('b_eco_4syv8vmm_mc', {}, { cid: 'c_eco_6akr4bgy' });
        }
        const STATE_RESULT = { isManagePoi, source };
        historyPathnamePush(
          this.props.history,
          `/web/operation/goods/${getRouterConfig(source)}#/rms-goods/waimai/batch-autobind-history`,
          true,
          { STATE_RESULT }
        );
      };
      const hasPermission = checkhasPermissionSync([
        getWmPermissionCode(WMActionEnum.BindWmChainGoods, source, WmBindActionEnum.HISTORY) as number,
      ]);
      return (
        <>
          {this.state.activeKey === WmTabPanelEnum.BIND_CHAIN_GOODS_LIST && hasPermission ? (
            <a onClick={goPublish}>菜品关联/解绑历史记录</a>
          ) : null}
        </>
      );
    };

    renderNotAssociatedAttrList = () => {
      const { activeKey, switchStatusMap, oldSwitchStatusMap, associateMode } = this.state;
      //是否是 连锁门店 视角
      const isBranchPoi = this.props.userService.isBranchPoi();
      //是否是 堂食外卖菜品关联
      const isWmBindList = [TsTabPanelEnum.美团外卖关联, TsTabPanelEnum.饿了么外卖关联].includes((activeKey as unknown) as TsTabPanelEnum);
      const isShowTab =
        isBranchPoi &&
        isWmBindList &&
        isShow(!!switchStatusMap?.[SWITCH_KEY_ENUM.启用外卖属性库], oldSwitchStatusMap?.[SWITTCH_TYPE.SSU_MGMT], associateMode);
      return isShowTab ? (
        <a
          className={styles['not-associated-attr-list']}
          onClick={() => {
            const params = new URLSearchParams(this.props?.location?.search);
            params.set(
              'entrance',
              activeKey === TsTabPanelEnum.美团外卖关联
                ? AttrEntranceEnum.门店美团外卖堂食外卖菜品关联
                : AttrEntranceEnum.门店饿了么外卖堂食外卖菜品关联
            );
            historyPush(this.props.history, `/waimai/not-associated-attr/list?${params}`);
          }}
        >
          属性未关联列表
        </a>
      ) : null;
    };

    renderFieldsSettingButton = (absolute?: boolean) => {
      let className: string = '';

      if (absolute) {
        className = this.props.isTangshi ? styles['tangshi-fiels-setting'] : styles['position-fields-setting'];
      }

      if (this.state.activeKey === WmTabPanelEnum.WAIMAI_GOODS_LIST || absolute) {
        return <FieldSettingButton className={className} hidePadding onClick={() => this.onFieldsSetting(false)} />;
      }
    };

    renderExtraContent = () => {
      return (
        <>
          {this.renderPublishHistory()}
          {this.renderNotAssociatedAttrList()}
          {this.renderFieldsSettingButton()}
        </>
      );
    };

    onTabChange = async (key: WmTabPanelEnum & TsTabPanelEnum) => {
      this.setState({ activeKey: key });
      if (key === WmTabPanelEnum.WAIMAI_GOODS_LIST) {
        const { switchStatusMap, taxRateSwitchStatus } = await this.getSwitchStatus();
        // 重新初始化字段设置
        this.onFieldsSetting(true, {
          switchStatusMap: switchStatusMap ?? void 0,
          taxRateSwitchStatus,
          abStrategyMap: this.state.abStrategyMap,
        });

        if (isManagePoi) {
          const oldPoiId = this.poiRef.virtualPoiId ? Number(this.poiRef.virtualPoiId) : this.poiRef.poiId;
          const newPoiId = usedPoiId();
          // 当前Tab等于门店美团外卖菜品时，如果门店发生了改变，需要更新查询参数并刷新
          if (key === WmTabPanelEnum.WAIMAI_GOODS_LIST && oldPoiId !== newPoiId) {
            // 更新mode之前，先将当前的状态保存起来
            const oldStatusMap = {
              syncStatus: this.model.syncStatus,
            };
            this.updateModel(oldStatusMap);
            this.getCategoryTree(true);
            this.poiRef = {
              poiId: poiConfig.poiId,
              orgId: poiConfig.orgId,
              virtualPoiId: poiConfig.virtualPoiId,
              relationPoiId: getRelationPoiId(),
            };
            this.setState({ selectedKeys: ['all'] });
            this.treeReportRef?.setQuery(
              {
                org: usedPoiId(),
                pageNo: 1,
              },
              true
            );
          }
        } else {
          // 注意：列表加载成功之后，切换时需要刷新列表数据，因为在「门店与集团菜关联页面」关联集团菜品之后，id是被替换了，不刷新编辑菜品会报错
          this.reportRefresh();
        }
      }
      if (key === TsTabPanelEnum.门店与集团外卖属性关联) {
        this.bindChainAttrListTabRef?.current?.refreshByNewPoi();
      }
    };

    /**
     * 抽离状态为FAIL的渲染逻辑
     * @param tabPaneChildren
     * @returns
     */
    renderTabPane = (tabPaneChildren: React.ReactElement | null) => {
      const { syncStatus } = this.model;
      return syncStatus === SyncStatusEnum.FAIL ? this.renderError() : tabPaneChildren;
    };

    render() {
      const { wmListTabText, activeKey } = this.state;

      // 集团穿透管理前置门店选择器：一店多开 连锁门店 + 门店&开通权限 都需要展示门店选择器
      // isCurrentRoute 如果不在当前页面 也展示 modal，解决bug https://ones.sankuai.com/ones/product/13924/workItem/defect/detail/83401084?activeTabName=first
      if ((isManagePoi || this.isSingleHasVirNotTangshi) && (!usedPoiId() || !this.state.isCurrentRoute)) {
        return <SelectPoiModal isVirtual={checkVirtual()} source={source} onOk={this.onOkInGlobalModalPoiSelector} showShopId={true} />;
      }

      return (
        <>
          {!this.props?.isTangshi && this.showTabs ? (
            <Spin
              wrapperClassName='syncStatus-spin'
              spinning={this.model.syncStatus === SyncStatusEnum.PENDING}
              tip='等待外卖菜数据同步...'
            >
              <Tabs
                activeKey={activeKey}
                className='goods-list-container'
                tabBarExtraContent={this.renderExtraContent()}
                onChange={this.onTabChange}
              >
                <TabPane tab={wmListTabText} key={WmTabPanelEnum.WAIMAI_GOODS_LIST}>
                  {this.renderTabPane(this.renderWmGoodsList())}
                </TabPane>
                {/** 门店与集团外卖菜关联 */}
                {this.renderBindWmChainList()}
                {!!this.state.switchStatusMap?.[SWITCH_KEY_ENUM.启用外卖属性库] && this.showWmTabs ? (
                  <TabPane
                    tab='门店与集团外卖属性关联'
                    key={TsTabPanelEnum.门店与集团外卖属性关联}
                    menuCode={
                      source === WaimaiSourceEnum.MEITUAN
                        ? WaimaiMenuCodes.MT_BIND_CHAIN_ATTR_LIST_TAB
                        : WaimaiMenuCodes.ELE_BIND_CHAIN_ATTR_LIST_TAB
                    }
                  >
                    {this.renderTabPane(this.renderAttrBind())}
                  </TabPane>
                ) : null}
                {this.getIsRenderGoodsList() ? (
                  <TabPane
                    tab='外卖菜品关联堂食'
                    key={source === WaimaiSourceEnum.MEITUAN ? TsTabPanelEnum.美团外卖关联 : TsTabPanelEnum.饿了么外卖关联}
                  >
                    {this.renderTabPane(this.renderWmBind())}
                  </TabPane>
                ) : null}
              </Tabs>
            </Spin>
          ) : (
            <>
              {this.renderWmGoodsList()}
              {this.renderFieldsSettingButton(true)}
            </>
          )}
        </>
      );
    }
  }
  return withRouteLifeCycle(withRouter(wrapContext(List)));
};

export default WaimaiGoodsList;

type List = ReturnType<typeof WaimaiGoodsList>;

export { List };
