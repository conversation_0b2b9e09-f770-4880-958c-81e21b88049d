/* eslint-disable @typescript-eslint/no-non-null-assertion */
import * as React from 'react';
import { toJS, set } from 'mobx';
import { observer } from 'mobx-react';
import { IV } from '@mtfe/sjst-antdx/es/common/utils';
import { unitModel } from '@rms-goods/models/goodsAttr/goodsUnit';
import {
  FormPageCard, 
  FormX, Modal, message, Spin, 
  Icon,
  Anchor,
} from '@mtfe/sjst-antdx';
import { REMARK_KEY }from '@rms-goods/consts/GoodsRemarks'
import { Button, Alert } from '@mtfe/sjst-antdx-saas';
import { moduleClick, pageView } from '@mtfe/next-biz/es/utils/analytics';
import formDataUtil from '@rms-goods/utils/goods/goodsFormValue';
import { minus, times, plus } from 'number-precision';
import { ComboGroupTO, Sku } from '@typings/NetTypings/responses/GET/api/v1/goods/chain/combo/detail';
import { CustomTxtVersionControl } from '@mtfe/next-biz/es/components/Version';
import { ComboChoiceTypeEnum, ComboGroupTypeEnum, getGoodsService, GroupCanCheckBoxEnum } from '@rms-goods/services/goods';
import PermissionEnum from '@rms-goods/utils/permissionCode';
import { userInfoUtils } from '@mtfe/next-util';
import {
  CommonConfirmEnum,
  ComboSettingSkuPriceEnum,
  MethodPriceType,
  CreateOrEditRouteEnum,
  GoodsStatusEnum,
  TaxDepartmentInheritEnum,
  GoodsBoxSaleSetting,
  SWITTCH_TYPE,
  ChannelEnum,
  ChannelPriceCodeMap,
  ManageInventoryEnum,
  ChannelDisplayStatus,
  FORCE_REMOVE_STANDARD_GOODS_REL,
} from '@typings/GoodsTypings/enum';
import { RouteComponentPropsWithQuery, History, historyPush } from '@mtfe/next-router';
import {
  historyPathnamePush,
} from '@mtfe/next-router-v2';
import {
  GoodsTypeEnum,
  createEditModel,
  Goods,
  methodSelectModel,
  taxAndDepartmentModel,
  goodsBoxModel,
  goodsSpecModel,
  shelfLifeDaysUnitModel,
  SpuChannelPropertiesItem,
  DetailOption,
} from '@rms-goods/models/goods';
import { brand as brandModel } from '@rms-goods/models/brand';
import { submitPrintConfigIds } from '@mtfe/next-biz/es/utils/printConfigParse';
import pinyin from '@mtfe/pinyin';
import { checkhasPermissionSync } from '@mtfe/next-biz/src/services/permission';
import isManagePoiConfig, { getUrlSuffix } from '@mtfe/next-biz/es/utils/managePoi';
import { getService, UserService } from '@mtfe/next-biz/es/services/user';
import { showMoreSetting, showDisplaySet, ManualCateforyPostion } from '@rms-goods/root/src/utils/goods/method';
import { AnchorsList, GetAnchorsList } from './AnchorTabs/constants';
import { comboGroupType } from './GroupComboSelect/GroupTable';
import { checkIsEmpty, isNotEmpty } from '@rms-goods/root/src/utils/noEmpty';
import { ABtestEnum, isAB } from '@rms-goods/root/src/components/AB';
import _ from "lodash"
import { BasicInfoPart1, BasicInfoPart2 } from './Form/BasicInfo';
import Attribute from './Form/Attribute';
import SaleInfo from './Form/SaleInfo';
import FinanceInfo from './Form/FinanceInfo';
import DisplayInfo from './Form/DisplayInfo';
import PrintConfig from './Form/PrintConfig';
import GroupSelect from './GroupComboSelect';
// import StandardsEdit from './StandardsEdit';
import { getRenderTimeOfComponent, PAGE } from '@mtfe/next-biz/es/lib/customDimension/render';
import { CatalogSceneEnum } from '@typings/GoodsTypings/enum';
import { getCatalogProperties } from '@rms-goods/root/src/utils/catalogProperties';


import './index.less';
import { throttleFn } from '@mtfe/next-biz/es/utils/throttle';
import { CheckModalTxt } from '@mtfe/next-biz/es/components/Version/plainTxt/pc';
import { checkerPropertyMutexRule } from './Form/Attribute/utils';
import { SeniorPrintModal } from '@rms-goods/root/src/models/printSettings';

import moment from 'moment';
import { UNV_BRAND } from '@rms-goods/root/src/services/brand';
import { SkuBox } from '@typings/NetTypings/responses/GET/api/v1/goods/chain/spu/get-detail';
import { SWITCH_KEY, SWITCH_KEY_ENUM } from '@typings/GoodsTypings/switch';
import { Combo } from '@rms-goods/root/src/utils/goods/comboData';
import { NO_BOX_OPT_ID } from '@rms-goods/root/src/components/GoodsBoxSelect/utils';
import { getConfigService } from '@rms-goods/root/src/services/config';
import { arr2Map } from '@rms-goods/root/src/utils/map';
import {
  getSelectedChannelList,
  handleStatusForm,
  handleDisplayForm,
  RecordIdInfo,
  handelChannelCategoryForm,
} from './helper';
import { parseControlledField } from '@rms-goods/root/src/utils/goods/multiChannel';
import { PRICE_CODE_LIST } from '@rms-goods/root/src/consts/GoodsMultiPrice';
import SkusEdit from './SkusEdit';
import { FormInstance } from '@mtfe/rmsform-antd';
import { map2PriceList, WRAPPER_FIELD } from './SkusEdit/utils';
import { getGoodsOnlyBaseCategory, getIsDisplayOpen } from '@rms-goods/root/src/utils/multiChannelManage';
import { getTransformSwitchConfigMapV1 } from '@rms-goods/root/src/utils/goods/getSwitchConfigMap';
import { checkUnitNeedWarning, UnitBusinessEnum } from '@rms-goods/root/src/utils/goods/check';
import { asyncConfirmModel } from '@rms-goods/root/src/components/FormComponent/ConfirmModel';
import { CHECK_SPU_RELATE, CHECK_UNIT_OPTION } from '@rms-goods/root/src/components/FormComponent/ConfirmModel/msg';
import { deleteGoodsConfirm } from '@rms-goods/root/src/components/CancelConfirm';
import { channelCategoryLengthChecker, MAX_CHANNEL_CATEGORY_TEXT } from '@rms-goods/root/src/utils/goodsCategory/display';
import { transformSpuId } from '@rms-goods/root/src/utils/sideGroup';
import { getShowSpuAttributeProperty } from '@rms-goods/root/src/services/common';
import { sellableStore } from './ComboRulesStore';
import { checkSaleCombo } from '../../../utils';
import { checkComboHalt } from '@rms-goods/root/src/utils/goods/checkComboHalt';
import { CLEAR_METHODS_TEXT } from './SideGroupTable/constant';
import { CREATE_OR_EDIT_GOOD_FE_KEY } from '@rms-goods/root/src/utils/createOrEdit';

const { Link } = Anchor;

const CREATE_DISH_PV: { [key in number]: string } = {
  10: 'c_eco_ng0000008',
  20: 'c_eco_ng0000009',
};

const EDIT_DISH_PV: { [key in number]: string } = {
  10: 'c_eco_ng0000010',
  20: 'c_eco_ng0000011',
};

export interface IProps extends RouteComponentPropsWithQuery<{
  type?: number;
  id?: string;
}> {
  type: GoodsTypeEnum;
  history: History;
  // 样式
  id: string;
  _onReport?: () => void;
}

interface State {
  // 用来销毁表单组件，清空其内存中的数据
  clear: boolean;
  loading: boolean;
  boxChargeType?: GoodsBoxSaleSetting;
  isSubmitButtonDisabled: boolean;
  isChain?: boolean;
  Anchors: AnchorsList[];
  moreSetting: boolean;
  showRemarkTwoCol: boolean;
  displaySet: boolean;
  userService?: UserService;
  showAnch?: boolean;
  isSinglePoi: boolean;
  remarkNum: number;
  visibleRuleDetail?: boolean;
}

export const getCreateOrEdit = (action: CreateOrEditRouteEnum) => {
  @observer
  class CreateOrEdit extends React.Component<IProps, State> {
    // 内联的 modal 挂载容器，必须每次都是随机 id，不然会有 bug - liyagntao
    containerId = `rms-goods-create-edit-${Date.now()}-${Math.random()}-modal`;

    skusEditForm?: FormInstance;

    statusForm!: FormX;

    categoryForm!: FormInstance;

    groupFrom!: { form: { form: FormX }[] };

    goodsForm!: FormX;

    attrForms: { groupForm?: FormX }[] = [];

    // 是否保存并继续添加
    continue: boolean = false;

    currentTime = new Date();

    recordInstance?: RecordIdInfo;

    /** 本地存储一下历史状态, 用于跳转时取值, 避免切换Tab导致location状态丢失 */
    locationState: IV = {};

    constructor(props: IProps) {
      super(props);
      /** 初始化生成实例 */
      this.recordInstance = new RecordIdInfo();
      this?.addRecord('constructor');
      
      this.state = {
        clear: false,
        loading: false,
        isChain: undefined,
        isSubmitButtonDisabled: false,
        moreSetting: false,
        showRemarkTwoCol: false,
        displaySet: false,
        Anchors: GetAnchorsList(props),
        showAnch: false,
        isSinglePoi: false,
        remarkNum: 1,
        visibleRuleDetail: false,
      };
    }

    /** 套餐编辑场景下需要记录 id 信息 */
    get isRecordCombo () {
      const type = this?.props?.match?.params?.type ?? GoodsTypeEnum.SPU;
      const isEdit = action === CreateOrEditRouteEnum.EDIT;
      return isEdit && (Number(type) === GoodsTypeEnum.COMBO);

    }

    /**获取当前套餐的经营状态 */
    get unSaleStatus(){
      const {goods, abStrategyMap, switchLatestStatusMap} = createEditModel;
      return checkComboHalt(goods, abStrategyMap, switchLatestStatusMap);
    }

    addRecord = (title: string, v?: IV) => {
      if(this.isRecordCombo) {
        this?.recordInstance?.add?.(title, createEditModel?.goods, this?.goodsForm?.valueMap, v);
      }
    }

    onReptor = () => {
      if(this.isRecordCombo) {
        this.recordInstance?.onReport?.();
      }
    }

    async queryABStrategy(option?: { isChainPage?: boolean }) {
      // wq解耦需求：增加新的白名单来控制点餐端展示字段是否显示    
      const [isWQ, isMultiChannel, showDiancan, isYuanjiMethodGroupLimit, isMainMeal] = await Promise.all([isAB(ABtestEnum.味千), (await getConfigService()).checkMultiChannel(), isAB(ABtestEnum.点餐端展示), isAB(ABtestEnum.袁记总部临时放开做法分组限制), isAB(ABtestEnum.正餐配菜)]);
      // 袁记总部临时放开做法分组限制，此处对白名单添加视角判断，减少调用位置视角判断逻辑
      const abStrategyMap = { [ABtestEnum.味千]: !!isWQ, [ABtestEnum.多渠道]: !!isMultiChannel, [ABtestEnum.点餐端展示]: !!showDiancan, [ABtestEnum.袁记总部临时放开做法分组限制]: !!isYuanjiMethodGroupLimit && option?.isChainPage, [ABtestEnum.正餐配菜]: isMainMeal }
      createEditModel.setAbStrategyMap(abStrategyMap);
      return abStrategyMap
    }

    async componentDidMount() {
      // 初始化时候存储一下location状态, 用于后续跳转
      this.locationState = {...(this.props.location?.state ?? {})};

      const { type, id } = (this.props.match && this.props.match.params) || { type: undefined, id: undefined };
      const userService = await getService();
      const chargeType = (!userService.isHeadOffice() || isManagePoiConfig()) ? (await goodsBoxModel.getBoxSaleSetting()).boxChargeTypeSetting.chargeType : undefined;
      const isChain = userService.isHeadOffice();
      const isSinglePoi = userService.isSinglePoi();
      const isSpu = Number(type) === GoodsTypeEnum.SPU;
      const isChainPage = userService.isHeadOffice() && !isManagePoiConfig();

      if (id) {
        pageView(EDIT_DISH_PV[Number(type)])();
      } else {
        pageView(CREATE_DISH_PV[Number(type)])();
      }
      const isDetail = typeof type !== 'undefined' && typeof id !== 'undefined';
      // 老开关
      const bizSwitchList = [];
      // 新开关
      const bizLatestSwitchList = [
        SWITCH_KEY[SWITCH_KEY_ENUM.PROPERTY_MUTEX_RULES],
        SWITCH_KEY[SWITCH_KEY_ENUM.SIDE],
        SWITCH_KEY[SWITCH_KEY_ENUM.餐盒支持自定义品牌和分类],
        SWITCH_KEY[SWITCH_KEY_ENUM.STATISTICAL_TRAFFIC_DISHES],
        SWITCH_KEY[SWITCH_KEY_ENUM.SINGLEPOI_SHOW_ORDER_TAG],
        SWITCH_KEY[SWITCH_KEY_ENUM.菜品设置打印描述],
        SWITCH_KEY[SWITCH_KEY_ENUM.推荐标签],
        SWITCH_KEY[SWITCH_KEY_ENUM.设置菜品重量],
        SWITCH_KEY[SWITCH_KEY_ENUM.设置可选套餐子菜限购份数],
        // v5.44.10新增自定义菜品规格编码,就不用老开关THIRD_PARTY_SKUID
        SWITCH_KEY[SWITCH_KEY_ENUM.自定义菜品规格编码],
        SWITCH_KEY[SWITCH_KEY_ENUM.菜品多渠道管理],
        SWITCH_KEY[SWITCH_KEY_ENUM.菜单模板分渠道管理], 
        SWITCH_KEY[SWITCH_KEY_ENUM.菜品分类分渠道管理], 
        SWITCH_KEY[SWITCH_KEY_ENUM.菜品库信息分渠道管理], 
        SWITCH_KEY[SWITCH_KEY_ENUM.管控方案分渠道管理],
        SWITCH_KEY[SWITCH_KEY_ENUM.展示标签],
        SWITCH_KEY[SWITCH_KEY_ENUM.菜品支持二次确认后再下单制作],
        SWITCH_KEY[SWITCH_KEY_ENUM.菜品做法支持图片],
        SWITCH_KEY[SWITCH_KEY_ENUM.菜品库支持维护酒水类标品关联物品],
        // 查询大小图开关
        SWITCH_KEY[SWITCH_KEY_ENUM.点餐页展示大图],
        //按规格维度设置点餐端展示开关状态
        SWITCH_KEY[SWITCH_KEY_ENUM.按规格维度设置点餐端展示],
        SWITCH_KEY[SWITCH_KEY_ENUM.菜品支持不设置默认规格],
        // 锅底支持先下单
        SWITCH_KEY[SWITCH_KEY_ENUM.支持设置锅底套餐],
        // 单店开启白名单后需要判断正餐配菜业务开关
        SWITCH_KEY[SWITCH_KEY_ENUM.正餐配菜],
      ];
      if (isSpu) {
        bizSwitchList.push(...[SWITTCH_TYPE.METHOD, SWITTCH_TYPE.WEIGHT, SWITTCH_TYPE.GOODS_COOKING_TIME]);
        bizLatestSwitchList.push(SWITCH_KEY[SWITCH_KEY_ENUM.SPECIFICATION_PICTURE_SETTING],SWITCH_KEY[SWITCH_KEY_ENUM.支持快速创建多规格菜品]);
        bizLatestSwitchList.push(SWITCH_KEY[SWITCH_KEY_ENUM.SPECIFICATION_PICTURE_SETTING], SWITCH_KEY[SWITCH_KEY_ENUM.菜品做法加料排序规则]);
      }

      this?.addRecord('componentDidMount getDetail前');

      const [, catalogProperties, attributeProperties] = await Promise.all([
        this.queryABStrategy({ isChainPage }),
        getCatalogProperties(isSpu ? CatalogSceneEnum.SPU : CatalogSceneEnum.COMBO),
        getShowSpuAttributeProperty(),
        isDetail
          ? this.getDetail(id!, type!, createEditModel.goods.firstCategoryId, {
            // 连锁门店的复制场景需要清空sku上的三方编码, 因为连锁门店创建时不需要该字段, 逻辑见Pages/rms-goods/src/pages/Goods/Tangshi/Actions/CreateOrEdit/SkusEdit/index.tsx, thirdPartySkuIdConf函数
            clearThirdPartySkuId: (action === CreateOrEditRouteEnum.COPY) && userService.isBranchPoi(),
            // 复制场景需要清空富文本审核状态
            clearRichTextAudit: action === CreateOrEditRouteEnum.COPY,
            //复制场景更新配菜分组id
            copyResetSideSpusGroupIds: action === CreateOrEditRouteEnum.COPY
          })
          : Promise.resolve({}),
      ]);

      this?.addRecord('componentDidMount getDetail后');

      try {
        createEditModel.getAllSpecs();
        // 编辑页面 getDetail 调用 getPrintsConfig 传入 id
        !isDetail && createEditModel.getPrintsConfig();
        // 总部有品牌字段
        userService.isHeadOffice() && brandModel.getBrandList();
        createEditModel.getTags();
        unitModel.getUnitList({}).then(() => {
          createEditModel.setDefaultUnit(unitModel.unitList);
        });
        // 老开关有值时请求
        if(bizSwitchList?.length) {
          createEditModel.setupSwitchStatus(bizSwitchList);
        }
        await createEditModel.setupSwitchLatestStatusMap(bizLatestSwitchList);
        await createEditModel.setServers({
          /**集团 */
          isChain,
          /**集团视角 */
          isChainPage,
          /**单门店 */
          isSinglePoi,
          /**穿透 */
          isManagePoi: !!isManagePoiConfig()!,
        })
        // 普通菜有制作时长
        isSpu && createEditModel.fetchCookingTimeUnitList();
        // 普通菜有保质期
        isSpu && shelfLifeDaysUnitModel.fetchShelfLifeDaysUnitList();
        isSinglePoi && +(type || GoodsTypeEnum.SPU) === GoodsTypeEnum.COMBO && createEditModel.getGrouponPlatformList();
        taxAndDepartmentModel.getTaxAndRate();
        SeniorPrintModal.Init();
        createEditModel.getChannelList();
        createEditModel.setCatalogProperties(catalogProperties);
        createEditModel.setAttributeProperties(attributeProperties?.tenantAttributes);
      } catch (error) {
        console.log(error);
      };

      const value = { ...(this.goodsForm ? this.goodsForm.valueMap || {} : {}), ...toJS(createEditModel.goods) };
      const { remarks } = value;
      const remarkNum = remarks && remarks.length > 0 ? formDataUtil.getRemarksNum(remarks) : 1;    // 初始化计算渲染自定义字段个数
      const moreSetVal = showMoreSetting(value, isSpu);
      this.setState({
        isChain,
        userService,
        boxChargeType: chargeType,
        moreSetting: moreSetVal,
        remarkNum,
        displaySet: showDisplaySet(value),
        isSinglePoi,
      });

      createEditModel.isReady = true;

      this?.addRecord('componentDidMount 结束');
      const isEdit =  !!(this.props.match && this.props.match.params && this.props.match.params.id);
      const isCopy = action === CreateOrEditRouteEnum.COPY;
      if ((isEdit || isCopy) && !isSpu) {
        this.validateSellable();
      }
      try {
        setTimeout(() => {
          this.props._onReport && this.props._onReport();
        }, 0);
      } catch (error) {
        console.error(error);
      }
    }

    UNSAFE_componentWillReceiveProps(nextProps: IProps) {
      const printConfigListPromise = createEditModel.getPrintsConfigSimple();
      const brandListPromise = userInfoUtils.isChain() ? brandModel.getBrandList() : Promise.resolve([]);
      // 在切换页签 切回来后 更新数据 这里可能更新不全 还要zhongguoxin看看补充下 没找到类别的更新接口
      // 页签切换回来 列表有最新数据
      Promise.all([
        createEditModel.getAllSpecs(),
        printConfigListPromise,
        createEditModel.getTags(),
        unitModel.getUnitList({}),
        brandListPromise,
      ]);
    }

    shouldComponentUpdate() {
      const id = (this.props.match && this.props.match.params && this.props.match.params.id);
      if (!(id && !createEditModel.goods.id) && !this.state.showAnch) {
        this.setState({
          showAnch: true,
        });
      }
      return true;
    }

    UNSAFE_componentWillMount() {
      this?.addRecord('UNSAFE_componentWillMount 数据复位前');

      // 创建组件销毁，相关数据复位
      createEditModel.reset();

      this?.addRecord('UNSAFE_componentWillMount 数据复位后');
    }

    getDetail = async (spuId: string, spuType: GoodsTypeEnum.COMBO | GoodsTypeEnum.SPU, categoryId: typeof createEditModel.goods.firstCategoryId, option: DetailOption) => {
      await createEditModel.getGoodsDetail(spuId, spuType, option);
      // XXX 组件 CategorySelect.componentDidMount 有相同实现，不知道这里有什么用 先去掉
      // await categorySelect.setSecondCates(createEditModel.goods.firstCategoryId);
    }

    setSubmitDisabled = (isDisabled: boolean) => this.setState({ isSubmitButtonDisabled: isDisabled });

    // 表单 change 回调
    formOnChange = async (formValue: Goods & { _spuSidesConfig?: string, _categoryId?: string[] }, key: string, isEdit: boolean, type: GoodsTypeEnum) => {
      const val = { ...formValue };
      // 删除子表单数据，因为从外层取到的子表单，值有问题
      delete val.skus;
      // 组件库问题，一会儿多出几个字段，别问我为什么
      // fomX 多出了一些字段，删除掉
      delete val.comboGroupTOS;
      // 组件库问题，多出了一个 id
      delete val.id;

      if (key === 'canWeight') {
        if (this.goodsForm) {
          // 需要等待表单把值设置完毕后，再验证
          setImmediate(() => {
            this.goodsForm.check();
          });
        }
        val.unitId = createEditModel.setDefaultUnit(unitModel.unitList, val.canWeight);
        // 只要切换了称重状态，那么就重置起售份数
        val.minAmount = createEditModel.defaultMinAmount(val.canWeight === CommonConfirmEnum.YES);
        // 如果是称重菜，规格保留第一个
        if (val.canWeight === CommonConfirmEnum.YES && createEditModel.goods.skus) {
          // createEditModel.goods.skus = [createEditModel.goods.skus[0]];
          // 手动重置子表单值
          if (this.skusEditForm) {
            const datasource = this.skusEditForm?.getFieldValue('skus') || [];
            this.skusEditForm?.setFieldValue(
              'skus',
              [datasource[0]]
            );
            // 需要给val更新值, 否则会覆盖createEditModel导致还是旧值
            val.propertyMutexRules = createEditModel.updateMutexRulBySkus([datasource[0]]);
          }
        }
        // 切换到非称重菜时，移除关联的按重量加价的做法
        if (val.canWeight === CommonConfirmEnum.NO && val.methods) {
          // 分组内含按重量加价的做法，需要移除分组
          const deletedGroupIdMap: { [k: number]: boolean } = {};

          val.methods = val.methods.filter((method) => {
            const k = method.groupId!;
            if (method.changePriceType === MethodPriceType.WEIGHT) {
              // 如果删除的是默认做法
              if (+methodSelectModel.defaultOption.get(method.groupId!)! === +method.id!) {
                methodSelectModel.defaultOption.set(method.groupId!, 0);
              }
              if (!deletedGroupIdMap.hasOwnProperty(k)) {
                deletedGroupIdMap[k] = true;
              }
              return false;
            }
            // 分组内有其他做法。不能移除整个分组
            deletedGroupIdMap[k] = false;
            return true;
          });
          val.methodGroupConfigs = val.methodGroupConfigs?.filter(({ groupId }) => !deletedGroupIdMap[groupId]);
        }
      }

      // 商品名称被编辑，需要在保存时提示用户
      if (key === 'name') {
        val.letterMnemonicCode = pinyin.getCamelChars(val.name || '').replace(/[^A-Za-z0-9]/ig, '').toLocaleUpperCase();
      }

      // 选择不打印，则清除 id list，后端只依靠 list 有值与否来判断是否打印
      if (key === 'printConfig') {
        if (val.printConfig === CommonConfirmEnum.NO) {
          val.printConfigIds = [];
        } else if (!val.printConfigIds) {
          val.printConfigIds = [];
        }
      }

      // 拼音助记码
      // 主动修改，或者在编辑状态时，不会自动生成助记码
      if (key === 'letterMnemonicCode' || isEdit) {
        val.letterMnemonicCode = (val.letterMnemonicCode || '').toLocaleUpperCase();
      }

      // 如果更改的分类，需要将数组值放到 valueMap 中
      if (key === '_categoryId' && val._categoryId) {
        val.firstCategoryId = val._categoryId[0];
        val.secondCategoryId = val._categoryId[1];
        val.categoryId = val._categoryId.length > 1 && !isNaN(Number(val._categoryId[1])) ? Number(val._categoryId[1]) : Number(val._categoryId[0]);
      }

      // 品牌字段 brandId 变更，分类、餐盒需要跟着变
      // 需要把 model 中的、表单域中的，全部置为空
      if (key === 'brandId') {
        val.categoryId = undefined;
        val.firstCategoryId = undefined;
        val.secondCategoryId = undefined;
        val._categoryId = [''];
        val.cateDepartmentId = null;
        if (val.departmentInherit === TaxDepartmentInheritEnum.YES) {
          val.departmentTemplateId = null;
          val.departmentOrgId = null;
        }

        const brandId = formValue.brandId;

        // 非同一品牌、非通用品牌的餐盒清空，处理 model 和嵌套 form
        const skus = this.skusEditForm?.getFieldValue('skus') as IV[];
        if (!!skus?.length) {
          const filterFn = (box: SkuBox) => box.brandId === UNV_BRAND.brandId || box.brandId === brandId
          const _skus = skus?.map(sku => ({
            ...sku,
            skuBoxes: sku.skuBoxes?.filter(filterFn),
          }));
          this.skusEditForm?.setFieldValue('skus', _skus);
        }

        if (+type === GoodsTypeEnum.COMBO
          && val.boxId !== NO_BOX_OPT_ID
          && goodsBoxModel.box?.brandId !== UNV_BRAND.brandId
        ) {
          val.boxId = NO_BOX_OPT_ID;
          goodsBoxModel.boxId = NO_BOX_OPT_ID;
        }
      }

      if (key === 'departmentInherit') {
        if (val.departmentInherit === TaxDepartmentInheritEnum.YES) {
          if (this.state.isChain && !isManagePoiConfig()) {
            val.departmentTemplateId = createEditModel.goods.cateDepartmentId;
          } else {
            val.departmentOrgId = createEditModel.goods.cateDepartmentId;
          }
          setTimeout(() => this.goodsForm && this.goodsForm.check(false, (name => name === `${this.state.isChain ? 'departmentTemplateId' : 'departmentOrgId'}`)), 4);
        }
      }
      // 字段变为「否」时清空「适用团购平台」字段
      if (key === 'grouponCombo' && val.grouponCombo === CommonConfirmEnum.NO) {
        val.grouponPlatform = undefined;
      }

      if (key === 'channel' && createEditModel.isDisplayDifferent) {
        const oldChannel: number[] = [...createEditModel.goods?.channel ?? []]
        const currentChannel: number[] = [...val?.channel ?? []]

        if (oldChannel.length > currentChannel.length) {
          //取消勾选
          const n = oldChannel.find(c => !currentChannel.includes(c))
         
          this.skusEditForm?.getFieldValue(WRAPPER_FIELD)?.forEach((sku: IV, index: number) => {
            this.skusEditForm?.setFieldValue([WRAPPER_FIELD, index, '_differentiatedDisplayInfoList'], sku?._differentiatedDisplayInfoList?.filter((i: number) => i !== n))
          })
        } else {
          //勾选
          const n = currentChannel.find(c => !oldChannel.includes(c))

          this.skusEditForm?.getFieldValue(WRAPPER_FIELD)?.forEach((sku: IV, index: number) => {
            if (!sku?._differentiatedDisplayInfoList?.includes(n)) {
              this.skusEditForm?.setFieldValue([WRAPPER_FIELD, index, '_differentiatedDisplayInfoList'], sku?._differentiatedDisplayInfoList?.concat(n))
            }
          })
        }
      }
      
      // formOnChange 触发时, 针对套餐，当 id 不一致时记录 id
      if(this?.isRecordCombo) {
        // formOnChange 时会删 val 的 id【详见 393 行】, 所以这里 diff form 和 mobx
        if(val?.comboId !== createEditModel.goods?.id) {
          this?.addRecord('formOnChange 数据异常', val);
        }
      }

      // 一旦编辑过助记码，那么不再根据 name 自动生成
      set(createEditModel.goods, { ...toJS(createEditModel.goods), ...val }); 
      // 套餐分组必填字段依赖mobx的saleStatus，更新完saleStatus再触发校验
      if (key === 'saleStatus' || key === 'saleTime') {
        if (this.unSaleStatus) {
          if(this.groupFrom && this.groupFrom.form){
            await this.goodsForm.check().finally(async () => {
              await Promise.all(
                this.groupFrom.form.map(form => {
                  form.form.check();
                })
              )
            });
          }
        } else {
          let newComboGroupTOS = createEditModel.goods?.comboGroupTOS;
          newComboGroupTOS = newComboGroupTOS?.map(comboGroup => {
            const canCheckBoxYes = comboGroup.choiceType === ComboChoiceTypeEnum.fixed ? comboGroup.amount : comboGroup.upperCount;
            const requiredSku = comboGroup?.skus?.filter(sku => sku?.requiredSku === CommonConfirmEnum.YES);
            if ((requiredSku ?? [])?.length >= Number(canCheckBoxYes)) {
              return {
                ...comboGroup,
                canCheckBox: GroupCanCheckBoxEnum.NO,
              }
            }
            const saleSkus = comboGroup?.skus?.filter(sku => (sku?.saleStatus ?? sku?.status) === GoodsStatusEnum.START) ?? [];
            if (Number(canCheckBoxYes) > (saleSkus?.length ?? 0)) {
              return {
                ...comboGroup,
                canCheckBox: GroupCanCheckBoxEnum.YES
              }
            }
            return comboGroup;
          })
          const newVal = {
            ...val,
            comboGroupTOS: newComboGroupTOS,
          }
          set(createEditModel.goods, { ...toJS(createEditModel.goods), ...newVal });
        }
      }
    }

    // 88上
    // showSaleStatusRecommendModal = () => {
    //   const ModelContent = (
    //     <div></div>
    //   );
    //   showModal({
    //     title: '点餐端示例',
    //     wrapClassName: 'rms-goods',
    //     children: ModelContent,
    //     maskClosable: false,
    //     width: 650,
    //     okText: '我知道了',
    //     renderFooter:(cancelButton, okButton) => okButton.node,
    //     className: 'goods-display-diancan-example-container',
    //     bodyStyle: {
    //       height: 450
    //     }
    //   });
    // };


    showSaleModal = (goodId: number) => {
      const isEditOperation = createEditModel.goods.id && action !== CreateOrEditRouteEnum.COPY;
      const operationText = isEditOperation ? '编辑' : '创建';
      Modal.confirm({
        title: `${operationText}成功`,
        content: `菜品${operationText}成功，现在可以将菜品加入模板/方案。`,
        okText: '加入模板/方案',
        cancelText: '稍后配置',
        onOk: async () => {
          const brandId = createEditModel.goods.brandId;
          createEditModel.reset();
          historyPush(this.props.history, `/goods/sale-config/${brandId}/${goodId}`);
        },
        onCancel: () => this.navigateToGoodsList(),
      });
    }

    handleStatusItem = (val: IV, key: string) => {
      const formValue: SpuChannelPropertiesItem[] = [];
      // @ts-ignore
      const salesChannel = ChannelEnum[key];
      const isTiming = val.value === GoodsStatusEnum.TIMING;
      const stopTimeIsBefore = val?.time?.stopTime?.isSameOrBefore(this.currentTime);
      formValue.push({
        salesChannel,
        code: 'saleStatus',
        value: isTiming && stopTimeIsBefore ? GoodsStatusEnum.HALT : val.value, // 停售时间超过当前时间按停售传递
      });
      if (
        val.value === GoodsStatusEnum.TIMING
        && val?.time
        && Object.keys(val?.time)?.length
      ) {
        val?.time?.effectiveTime
          && formValue.push({
            salesChannel,
            code: 'effectiveTime',
            value: moment(val?.time?.effectiveTime).valueOf().toString(),
          });
        val?.time?.stopTime
          && formValue.push({
            salesChannel,
            code: 'stopTime',
            value: stopTimeIsBefore
              ? this.currentTime.valueOf().toString()
              : moment(val?.time?.stopTime).valueOf().toString(),
          });
      }
      return formValue;
    };

    handleResData(result: IV) {
      // 成功埋点
      this.submitSuccessMC();
      // 保存并继续添加
      const data = result?.chainsSpuTO || result;
      if (this.continue) {
        message.success('保存成功');
        this.continue = false;
        this.setState({
          clear: true,
        }, () => {
          // 需要等页面原有组件销毁了，再设置 clear
          setTimeout(() => {
            this.setState({
              clear: false,
            });
          }, 0);
        });
        // 初始化 model 值
        createEditModel.reset(true, this.goodsForm?.valueMap);
        // 设置默认单位
        createEditModel.setDefaultUnit(unitModel.unitList);
      } else {
        if (this.state.isChain && !isManagePoiConfig() && !this.props.matchQuery?.isMultiPoi && (data || createEditModel.goods.id) && checkhasPermissionSync([PermissionEnum.CONTROL_SCHME_CREATE, PermissionEnum.TEMPLATE_GOODS_CHANGE_PRICE])) {
          let goodId = data ? data.id : null;
          if (!goodId) {
            if (createEditModel.goods.type === GoodsTypeEnum.COMBO) {
              goodId = createEditModel.goods.spuId;
            } else {
              goodId = createEditModel.goods.id;
            }
          }
          this.showSaleModal(goodId);
        } else {
          this.navigateToGoodsList();
        }
      }
    }

    onForceRemove = async (formValue: IV) => {
      // forceRemoveStandardGoodsRel：强制删除标准菜品与关联菜品关系
      formValue.forceRemoveStandardGoodsRel = FORCE_REMOVE_STANDARD_GOODS_REL;
      const result = await createEditModel.saveGoods(formValue, action);
      this.handleResData(result);
    }

    validateSellable = async () => {
      try {
        sellableStore.validate(createEditModel);
      } catch (error) {
        // 处理表单校验错误
      }
    };

    onSubmit: <T extends Goods & Combo>(v: T, type: GoodsTypeEnum) => void = async (v, type) => {      
      const { switchLatestStatusMap, abStrategyMap, channelList } = createEditModel;
      const isChainPage = this.state.isChain && !isManagePoiConfig();
      const isMultiChannelAB = abStrategyMap?.[ABtestEnum.多渠道];
      const isMultiChannelManage = !!switchLatestStatusMap?.[SWITCH_KEY_ENUM.菜品多渠道管理];
      const switchStatusOrTypeMap = getTransformSwitchConfigMapV1(switchLatestStatusMap);
      const isGoodsInfoToChannel = !!switchStatusOrTypeMap?.[SWITCH_KEY_ENUM.菜品库信息分渠道管理]?.status;
      const isShowSaleChannel = isMultiChannelAB && isMultiChannelManage && (isChainPage ? isGoodsInfoToChannel : true);
      const isSpu = Number(type) === GoodsTypeEnum.SPU;
      
      // 套餐通过 diff comboId 数据看是否需要埋点上报
      if(this.isRecordCombo) {
        if(v?.comboId !== v?.id || createEditModel.goods?.comboId !== createEditModel.goods?.id) {
          this?.addRecord('onSubmit 数据异常', v);
          this?.onReptor();
        }
      }

      // XXX: 下方的isEdit是通过match.params.id判断的, 复制会走到编辑里面, 所以这里声明isCreateOrCopy用于新建场景(复制也是新建)
      const isCreateOrCopy = [CreateOrEditRouteEnum.CREATE, CreateOrEditRouteEnum.COPY].includes(action);

      // 套餐分组VO -> TO
      if (Array.isArray(v.comboGroupTOS)) {
        v.comboGroupTOS.forEach((item: ComboGroupTO & comboGroupType, index: number) => {
          if (item.type === ComboGroupTypeEnum.stable) {
            delete v.comboGroupTOS![index]?.amount;
            // 创建场景逻辑兜底，避免固定套餐分组出现重复可选数据
            if (isCreateOrCopy) {
              delete v.comboGroupTOS?.[index]?.canCheckBox;
            }
            item.skus?.forEach((sku) => {
              // 固定分组没有菜品加价字段
              delete sku?.priceChange;
              // add菜品的逻辑放在这里了.避免add时候是固定菜品切换到可选菜品的时候可选菜品缓存的默认值requiredSku就变成了YES 还可以防可选的值影响到固定出参,这里固定的0和1
              sku.defaultSku = CommonConfirmEnum.NO;
              sku.requiredSku = CommonConfirmEnum.YES;
            });
          }

          // 经营标准下区间选择设置N1、N2默认值
          if (item.choiceType === ComboChoiceTypeEnum.between && !this.unSaleStatus) {
            item.lowerCount = isNotEmpty(item.lowerCount) ? item.lowerCount : 1;
            item.upperCount = isNotEmpty(item.upperCount) ? item.upperCount : 2;
          }

          // 修复切换为自动计算单价依然传了这两个字段
          if (v.settingSkuPrice === ComboSettingSkuPriceEnum.auto) {
            // 自动计算就删除缓存的百分比和价格;如果选过固定分组的话sku里面缓存了skuComboPrice和percent一并删除
            delete v.comboGroupTOS?.[index]?.price;
            delete v.comboGroupTOS?.[index]?.percent;
            item.skus?.forEach((sku) => {
              delete sku?.percent;
              delete sku?.skuComboPrice;
            });
          } else {
            // 手动计算且固定分组删除最外层的百分比入参
            if (item.type === ComboGroupTypeEnum.stable) {
              delete v.comboGroupTOS?.[index]?.price;
              delete v.comboGroupTOS?.[index]?.percent;
            } else {
              // 手动计算且可选分组删除表格内缓存的skuComboPrice和percent
              item.skus?.forEach((sku) => {
                delete sku?.percent;
                delete sku?.skuComboPrice;
              });
            }
          }
        });
      }
      // 新交互， 创建/编辑菜品，提交数据处理，当用户设置售卖状态为定时启停售时将组件DatePickers组件的saleTime值中取出对应字段放入需要上传的对应字段中
      if (v.saleStatus === GoodsStatusEnum.TIMING) {

        v.effectiveTime = v.saleTime?.effectiveTime ? v.saleTime.effectiveTime.valueOf() : null;

        v.stopTime = v.saleTime?.stopTime ? v.saleTime.stopTime.valueOf() : null;
      //如果在数据处理阶段，停售时间仍然早于或等于当前时间，则用户已经确认将售卖状态改为停售状态，并且启售/停售不传时间但是需要有effectiveTime和stopTime所以传null给后端。
        if(v.saleTime?.stopTime && moment(v.saleTime?.stopTime).isSameOrBefore(this.currentTime)){
          v.saleStatus = GoodsStatusEnum.HALT;
          v.stopTime = null;
          v.effectiveTime = null;
        }
       } else {
        v.effectiveTime = null;
        v.stopTime = null;
       }

      // 校验加料做法 Form
      if (this.attrForms.length) {
        let passCheck = true;
        await Promise.all(this.attrForms.map(ref => ref.groupForm?.check())).catch(() => {
          this.setState({ loading: false });
          passCheck = false;
        });
        if (!passCheck) return;
        const isEmptyGoodsGroup= this.checkGoodsGroupIsEmpty();
        if(isEmptyGoodsGroup) return;
      }

      // 如果开关开的情况下 校验互斥规则点餐冲突
      if (createEditModel?.switchLatestStatusMap?.[SWITCH_KEY_ENUM.PROPERTY_MUTEX_RULES] && v?.propertyMutexRules?.length) {
        const msg = checkerPropertyMutexRule(v.propertyMutexRules);
        // const newRuleArray = dealWithPropertyMutexGroupMethod(v.propertyMutexRules);
        if (msg) {
          Modal.error({
            title: '提交失败',
            content: msg,
            okText: '我知道了',
          });
          return;
        }
      }

      if (v.taxRateUuid && typeof (v.taxRateUuid) === 'object') {
        //@ts-ignore
        v.taxRateUuid = v.taxRateUuid?.key;
      }
      if (!v.categoryId) {
        //@ts-ignore
        v.categoryId = v._categoryId?.length > 1 && !isNaN(v._categoryId?.[1]) && Number(v._categoryId?.[1]) ? Number(v._categoryId?.[1]) : Number(v._categoryId?.[0]);
      }
      // 多渠道VO -> TO
      if (abStrategyMap?.[ABtestEnum.多渠道]) {
        if (isShowSaleChannel) {
          // 5.52开关切换总开关关闭无适用渠道字段，不存在适用渠道时不校验
          if (!v.channel?.length) return message.error('请选择适用渠道');
          
          const salesChannels = getSelectedChannelList(createEditModel.channelList, v);
          v.spuChannelProperties = [];
          v.spuChannelProperties?.push(...salesChannels);
        }
        // 后端点餐段展示字段表示
        // @ts-ignore
        v.useChannelDisplays = true

        if (!createEditModel.switchLatestStatusMap?.[SWITCH_KEY_ENUM.按规格维度设置点餐端展示]) {
          // 字段展示逻辑见PRD：https://km.sankuai.com/collabpage/1670066763
          if (!v.channelDisplays?.length && !abStrategyMap?.[ABtestEnum.点餐端展示]) {
            const displayReq = handleDisplayForm({
              formVal: {},
              v,
              isSwitchClose: true,
              notDiancanAbAndMutilChannel: true,
              channelList,
              switchStatusMap: switchLatestStatusMap,
              abStrategyMap,
              isShowSaleChannel,
            });
            // @ts-ignore
            v.channelDisplays = displayReq || [];
          }
        }
        
        if (this.statusForm) {
          let ifCheck = true;
          await this.statusForm.submit().then((formVal) => {
              const statusReq = handleStatusForm(formVal, v)
              // @ts-ignore
              v.spuChannelProperties?.push(...statusReq);
              if (!createEditModel.switchLatestStatusMap?.[SWITCH_KEY_ENUM.按规格维度设置点餐端展示]) {
                const isDisplayOpen = getIsDisplayOpen({
                  abStrategyMap,
                  switchLatestStatusMap,
                  isChainPage: this.state.isChain && !isManagePoiConfig()
                 });
                const displayReq = handleDisplayForm({
                  formVal,
                  v,
                  isSwitchClose: !isDisplayOpen,
                  channelList,
                  switchStatusMap: switchLatestStatusMap,
                  abStrategyMap,
                  isShowSaleChannel,
                });
                // @ts-ignore
                v.channelDisplays = displayReq || [];
              }
            })
            .catch((e) => {
              this.setState({ loading: false });
              this.continue = false;
              ifCheck = false;
            });
          if (!ifCheck) return;
        }

        /**
         * [5.52.10]fix: 开关切换时(菜品库分类由开->关, 开关请求过程中打开菜品库), 可能出现填写基础分类后报错显示渠道分类未填, 阻塞保存
         * 原因: 开关切换过程中进入时导致多渠道分类form挂载(因为这时候model残留上次多渠道的值), 后续只展示基础分类组件时, 在提交时还是会在下方校验多渠道form
         * 修正点: 多渠道分类只会在多渠道Form存在且展示多渠道组件时进行校验
         */
        const isShowBaseCategory = getGoodsOnlyBaseCategory({ abStrategyMap, switchLatestStatusMap, isChainPage});
        // 处理菜品分类form
        if (this.categoryForm && !isShowBaseCategory) {
          let ifCheck = true;
          try {
            await this.categoryForm?.check();
            const categoryFormVal = this.categoryForm?.getFieldsValue();
            // 校验通过后过滤基础分类，基础分类的值已经在 Change 时写入了 mobx 中，不需要额外处理
            const { categoryReq, baseItem } = handelChannelCategoryForm(categoryFormVal, v.channel);
            v.channelCategories = categoryReq;
            v.categoryId = baseItem?.categoryId;
            // spu类型上firstCategoryId， secondCategoryId为 string，但是实际交互使用的是 number，此处使用类型兼容一下
            v.firstCategoryId = baseItem?.firstCategoryId as unknown as string;
            v.secondCategoryId = baseItem?.secondCategoryId as unknown as string;
          } catch (e) {
            this.setState({ loading: false });
            this.continue = false;
            ifCheck = false;
            // 有报错时候校验一下是不是有渠道分类超过上限
            const categoryFormVal = this.categoryForm?.getFieldsValue();
            const checkCategoryUpLimit = Object.values(categoryFormVal ?? {})?.some(channelCategoryLengthChecker);

            // 根据分类对于报错提示相关文案
            message.error(checkCategoryUpLimit ? MAX_CHANNEL_CATEGORY_TEXT : '请先选择分类');
            return;
          }

          if (!ifCheck) return;
        }
      }
      // sku VO -> TO
      if (this.skusEditForm) {
        let ifCheck = true;
        try {
          const formValue = await this.skusEditForm.check()
          const isWeight = v.canWeight === CommonConfirmEnum.YES;
          let skus = formValue.skus as IV[];
          // 需要提交的渠道价格
          const priceCodeMap = arr2Map(v.channel || [], (code) => ({ key: ChannelPriceCodeMap[code as ChannelEnum]!, val: true }));

          skus = skus.map((s, i) => {
            // 保时洁审核状态每次查详情时后端获取返回，无需提交
            delete s.auditMultimedia;
            // 删除前端自定义字段
            delete s[CREATE_OR_EDIT_GOOD_FE_KEY.fe_spec_name];
            s.goodsPriceList = map2PriceList(s)
              // 根据所选的「适用渠道」范围提交渠道价格，不在适用渠道内的价格不提交
              .filter((priceTO: IV) => {
                const { priceCategoryCodes } = priceTO;
                // 不过滤基础多价格
                if (PRICE_CODE_LIST.includes(Math.max(...priceCategoryCodes))) return true;
                // 渠道 priceCategoryCode
                const channelPriceCode = Math.max(...(priceCategoryCodes || []));
                return !!priceCodeMap[channelPriceCode];
              });
            // boxId统一把0转换为null提交给后端
            if (s.boxId === 0) {
              s.boxId = null;
            }
            // 适配后端接口，以后可能一个sku对应多个barcode，数组中不应该有null或undefined
            if(s.barcode) {
              s.barcodeList = [s.barcode];
            } else {
              s.barcodeList = null;
            }

            /** 点餐端展示是否有编辑权限 */
            const hasSkuChannelDisplayPermission = createEditModel.checkSkuChannelDisplayPermission({
              isDescription: false,
              isEdit: !!this.props.match?.params?.id,
              isCopy: action === CreateOrEditRouteEnum.COPY,
              fieldControl: s?.fieldControl
            })

            //sku点餐端展示有编辑权限并且按规格维度设置点餐端展示开关为开才处理数据，否则原路返回
            if (hasSkuChannelDisplayPermission && createEditModel.switchLatestStatusMap?.[SWITCH_KEY_ENUM.按规格维度设置点餐端展示]) {
              if (createEditModel.isDisplayDifferent) {
                s.differentiatedDisplayInfoList = createEditModel.goods?.channel?.map((salesChannel) => ({
                  salesChannel,
                  display: s._differentiatedDisplayInfoList?.includes(salesChannel) ? ChannelDisplayStatus.SHOW : ChannelDisplayStatus.HIDE
                }))
              } else {
                s.universalDisplayInfo = {
                  salesChannel: ChannelEnum.基础渠道,
                  display: s._universalDisplayInfo
                }
              }
            }
            return s;
          });

          // 注意：当用户没填第三方菜品编码时，需要传空字符串到后端，与老前端区分开来
          if (isSpu) {
            // @ts-ignore
            v.skus = skus.map(sku => Object.assign(sku, { thirdPartySkuId: sku?.thirdPartySkuId || '' }));
            // 如果是称重菜，只要第一个规格，报表的数据已经失控了，传进去 value 在提交时不受控制，只能在提交时再次做处理
            if (isWeight) {
            // @ts-ignore
              v.skus = [v.skus[0]];
            }
            if (!abStrategyMap?.[ABtestEnum.味千]) {
              delete v.saleMode;
            }
            // 正餐配菜需求：将普通菜属性（sku）转化为分组属性（spu）
            v.sideSpus = transformSpuId(v.sideSpus ?? [])
          } else {
            // 字段值为「否」时提交空数组
            v.grouponPlatformCodes = v.grouponCombo === CommonConfirmEnum.YES && v.grouponPlatform
              ? [v.grouponPlatform]
              : [];
            //对于套餐在创建时数据发个中心所以仅stopTime字段需要放在spuProperties中，但是编辑套餐时数据发给后端，stopTime不放在spuProperties中所以为了兼容，内外都有stopTime字段
            const commonData = [
              // 后续这种为后端赋值的代码不允许再增加了，下一个加字段的需求里进行优化重构
              { code: 'footFall', value: skus[0].footFall },
              { code: 'stopTime', value: v.stopTime},
              { code: 'confirmBeforeMake', value: v.confirmBeforeMake},
            ];

            const getSpuProperties = () => {
              // 处理卡路里数据，避免000等情况，转为0
              const formatCalories = (value?: string) => {
                return checkIsEmpty(value) ? value : String(Number(value));
              };
              // 卡路里值转换
              const comboEstimatedCalories = formatCalories(v?.comboEstimatedCalories);
              // 创建卡路里属性对象
              const caloriesProperty = {
                code: 'comboEstimatedCalories',
                value: comboEstimatedCalories,
              };
              // 卡路里是否展示
              const showComboEstimatedCalories = createEditModel.getAttributeProperty('comboEstimatedCalories');
            
              // 集团
              if (this.state.isChain && !isManagePoiConfig()) {
                /**套餐-卡路里数据转换 */
                if (showComboEstimatedCalories && Number(type) === GoodsTypeEnum.COMBO) {
                  commonData.push(caloriesProperty);
                  v.comboEstimatedCalories = undefined;
                }
                return commonData;
              } 
              
              // 门店、穿透，卡路里白名单关，不走下面的添加字段
              if (!showComboEstimatedCalories) {
                return [];
              }
              
              // 新增场景
              if (action === CreateOrEditRouteEnum.CREATE) {
                return [caloriesProperty];
              }
              
              // 编辑场景
              const existingProperties = v?.spuProperties || [];
              const hasCaloriesProperty = existingProperties.some(
                (property) => property?.code === 'comboEstimatedCalories'
              );
              
              if (hasCaloriesProperty) {
                // 更新现有卡路里属性
                return existingProperties.map((property) => ({
                  ...property,
                  value: property?.code === 'comboEstimatedCalories' ? comboEstimatedCalories : property?.value,
                }));
              } 
              
              // 添加卡路里属性到现有属性列表
              return existingProperties.length ? [...existingProperties, caloriesProperty] : [caloriesProperty];
            };

            v = Object.assign(v, !abStrategyMap?.[ABtestEnum.味千]
              ? {
                ...skus[0],
                thirdPartySkuId: v?.thirdPartySkuId || '',
                spuProperties: getSpuProperties(),
              }
              : {
                ...skus[0],
                thirdPartySkuId: v?.thirdPartySkuId || '',
                spuProperties: [
                  { code: 'saleMode', value: v.saleMode },
                  ...getSpuProperties(),
                ],
              });
            if (!abStrategyMap?.[ABtestEnum.味千]) {
              delete v.saleMode;
            }
            
            // v.footFall = footFall
            v.footFall = skus[0]?.footFall;
          }
        } catch (error) {
          let indexArr = (error as { errorFields?: { name: IV, errors: IV }[] })?.errorFields?.map((ite) => {
            return ite?.name?.[1];
          });
          indexArr = Array.from(new Set(indexArr));
          const errorMsg = `菜品规格信息有误，请展开${indexArr?.map((ite) => `【规格${ite + 1}】`)}查看并修正后重新提交。`;
          if (indexArr?.length) {
            Modal.confirm({
              title: '提示',
              content: errorMsg,
              cancelButtonProps: { hidden: true },
              width: 600,
            });
          };
          this.setState({ loading: false });
          ifCheck = false;
          this.continue = false;
        };
        if (!ifCheck) return;
      }
      // 校验套餐分组子表单
      if (this.groupFrom && this.groupFrom.form) {
        await Promise.all(
          this.groupFrom.form.map(form => form.form.check()),
        ).catch(() => {
          this.setState({ loading: false });
          return Promise.reject();
        });
      }

       // 兼容分类选择器默认弹出required提醒 - 兜底分类选择
      // 放置在基础分类&多渠道菜品分类form（两处都有涉及基础分类处理）之后
      if (!v.categoryId) {
        message.error('请先选择分类');
        this.setState({ loading: false });
        return
      }

      if (createEditModel.nameChangeFlag && action !== CreateOrEditRouteEnum.COPY) { 
        const isEditFlag = await this.modifyNameModal(v, isSpu ? '菜品' : '套餐')
        if (!isEditFlag) { 
          this.setState({ loading: false });
          return
        }
      }

      /** 收敛校验逻辑, 当校验不通过时, 取消加载并取消提交 */
      const isHasError = await this.asyncCheckIsHasError(Number(type), v);
      if (!!isHasError) {
        this.setState({ loading: false });
        return;
      }
  
      const isEdit = !!(this.props.match && this.props.match.params && this.props.match.params.id);
      /**
       * 说明: 编辑情况沿用下方历史逻辑进行字段校验, 新建/复制情况下无鉴权, 不走该逻辑
       * 备注: 
       *   点餐端字段数据冗余: 现状[复制]/[编辑]时, 会在提交时出现多渠道点餐端字段冗余的情况, 历史实现上前后端均未根据适用渠道二次过滤, 使得实际菜品存储时会有点餐端字段数据冗余情况
       *   已和后端确定为历史实现可接受, 无隐患, 原因: 点餐端端菜品会根据适用渠道过滤, 对应渠道的菜品才会读取渠道上的点餐端展示字段数据, 冗余的点餐端数据因为没有适用渠道不会被使用
       */
      // XXX 无字段权限时渠道字段用详情接口返回回填，避免联动、过滤逻辑导致字段鉴权失败
      if(action === CreateOrEditRouteEnum.EDIT) {
        parseControlledField(v, createEditModel._detail);
      }
      // 菜品提交带上品牌
      const formValue = formDataUtil.formValueParse(v, {
        type,
        allSpecs: createEditModel.standards.allSpecs,
        brandId: createEditModel.goods.brandId,
        defaultOption: methodSelectModel.defaultOption,
      }, isEdit);
      if (this.state.isChain) {
        formValue.printConfigIds = submitPrintConfigIds(formValue.printConfig, formValue.printConfigIds);
      }
      if(formValue[REMARK_KEY]) {
        formValue[REMARK_KEY] = formValue[REMARK_KEY].slice(0,this.state.remarkNum)
      }
      formValue.checkNumMnemonicCode = CommonConfirmEnum.YES;
      // XXX 是否允许套餐被子菜联动隐藏，第一次提交时不允许
      if (+type === GoodsTypeEnum.COMBO) {
        formValue.allowHideBySubSku = CommonConfirmEnum.NO;
      }
      // POS端不会根据开关判断，他们只判断设置没设置最大值，我们这里给他整一个判断归零
      if (formValue.sideSpuCountConfig === CommonConfirmEnum.NO) formValue.maxSideSpuCount = 0;
      if (formValue.shelfLifeDays === (null || '')) formValue.shelfLifeDays = -1;
      let result: IV;
      if (+type === GoodsTypeEnum.COMBO) {
        let isNext = true;
        // 停售状态
        if (this.unSaleStatus) {
          // 发送请求，保存goods数据
          this.validateSellable()
          const unsaleGoodsSize = sellableStore.getUnsellbleGoodsSize;
          if (unsaleGoodsSize !== 0) {
            await new Promise((resole) => {
              Modal.confirm({
                title: '提示',
                content: `${unsaleGoodsSize}条套餐菜品信息有误，请根据灰色提示尽快优化，否则套餐无法销售。`,
                okText: '继续保存',
                cancelText: '返回修改',
                onCancel: () => {
                  isNext = false;
                  resole(false);
                },
                onOk: async () => {
                  isNext = true;
                  resole(true);
                },
              });
            })
          }
        } else {

          // 启售状态
          const error = checkSaleCombo(createEditModel);
          if (!!error) {
            Modal.error({
              title: '警告',
              content: (
                error
              ),
            });
            return;
          }
        }
        if (!isNext) return;
      }
      try {
          result = await createEditModel.saveGoods(formValue, action);
          this.handleResData(result);
      } catch (e) {
        let errorMsg = e.message;
        switch (e.code) {
          case 10000:
            Modal.confirm({
              title: '助记码重复',
              content: errorMsg,
              okText: '继续保存',
              cancelText: '返回修改',
              onOk: async () => {
                delete formValue.checkNumMnemonicCode;
                try {
                  const res = await createEditModel.saveGoods(formValue, action);
                  if (res) {
                    const goodId = res.chainsSpuTO ? res.chainsSpuTO?.id : createEditModel.goods.id;
                    // 门店视角和穿透管理不需要弹出引导销售设置
                    return this.state.isChain && !isManagePoiConfig() ? this.showSaleModal(goodId) : this.navigateToGoodsList();
                  } else {
                    this.navigateToGoodsList();
                  }
                } catch (e) {
                  Modal.error({
                    title: '操作失败',
                    content: e.message,
                    cancelText: '关闭',
                  });
                } finally {
                  this.setState({ loading: false });
                }
              },
            });
            break;
          case 28546:
            // 套餐中存在隐藏子菜，需要二次确认
            Modal.confirm({
              title: '提示',
              content: errorMsg,
              okText: '继续保存',
              cancelText: '返回修改',
              onOk: async () => {
                try {
                  // 允许套餐被分组中子菜联动隐藏
                  formValue.allowHideBySubSku = CommonConfirmEnum.YES;
                  const res = await createEditModel.saveGoods(formValue, action);
                  if (res) {
                    const goodId = res.chainsSpuTO ? res.chainsSpuTO.id : createEditModel.goods.id;
                    // 门店视角和穿透管理不需要弹出引导销售设置
                    return this.state.isChain && !isManagePoiConfig() ? this.showSaleModal(goodId) : this.navigateToGoodsList();
                  } else {
                    this.navigateToGoodsList();
                  }
                } catch (error) {
                  this.setState({ loading: false });
                  Modal.error({
                    title: '操作失败',
                    content: error.message,
                    cancelText: '关闭',
                  });
                }
              },
            });
            break;
          case 30001:
            errorMsg = '保存失败！当前存在部分字段不可编辑，请刷新后重新提交';
            Modal.error({
              title: '操作失败',
              content: errorMsg,
              cancelText: '关闭',
            });
            break;
          case 28331:
            deleteGoodsConfirm(e);
            break;
          case 38101: 
            // 二次删除弹窗
            const isNext = await asyncConfirmModel(CHECK_SPU_RELATE);
            if (isNext) { this.onForceRemove(formValue) };
            break;
          case 28554:
            Modal.confirm({
              title: '提示',
              content: '已关联外卖菜品的套餐信息必须填写正确，是否解除外卖菜品关联，继续保存套餐。',
              okText: '确定',
              cancelText: '取消',
              onOk: async () => {
                try {
                  const option = {
                    clearTsWmRel: CommonConfirmEnum.YES,
                  }
                  const res = await createEditModel.saveGoods(formValue, action, option);
                  if (res) {
                    const goodId = res.chainsSpuTO ? res.chainsSpuTO?.id : createEditModel.goods.id;
                    // 门店视角和穿透管理不需要弹出引导销售设置
                    return this.state.isChain && !isManagePoiConfig() ? this.showSaleModal(goodId) : this.navigateToGoodsList();
                  } else {
                    this.navigateToGoodsList();
                  }
                } catch (error) {
                  this.setState({ loading: false });
                  Modal.error({
                    title: '操作失败',
                    content: error?.message,
                    cancelText: '关闭',
                  });
                }
              },
            });
            break;
          default:
            Modal.error({
              title: '操作失败',
              content: errorMsg,
              // 修正弹窗报错文案，避免出现ok
              okText: '我知道了',
              cancelText: '关闭',
            });
        }
      } finally {
        this.setState({ loading: false });
      }
    }

    navigateToGoodsList() {
      // 只用在跳转前清理store即可
      createEditModel.reset();
      const suffix = getUrlSuffix(!!isManagePoiConfig());
      const { channelCode, pois } = this.locationState;
      // const channelCode = this.props.location?.state?.channel;
      // const pois = this.props.location?.state?.pois;
      const isMultiPoi = this.props.matchQuery?.isMultiPoi
      if (isManagePoiConfig()) {
        isMultiPoi ? historyPush(this.props.history, `/web/operation/goods/poi-goods-manage#/rms-goods/goods/management/multi-poi`, true, { setQuery: true, activeKey: channelCode ? `channel_${channelCode}` : undefined, pois })
          : historyPathnamePush(this.props.history, `/web/operation/goods/poi-goods-manage#/rms-goods/goods/management/list`, true, { setQuery: true, activeKey: channelCode ? `channel_${channelCode}` : undefined });
      } else {
        historyPush(this.props.history, `/web/operation/goods/list#/rms-goods/goods/list${suffix}`, true, { setQuery: true, activeKey: channelCode ? `channel_${channelCode}` : undefined });
      }
    }

    onContinue = async () => {
      if (this.goodsForm) {
        this.continue = true;
        try {
          ManualCateforyPostion(createEditModel.goods);
          await this.goodsForm.submit();
        } catch (e) {
          // 如果表单校验不通过，则重置标识位
          this.continue = false;
        }
      }
    }

    renderLoading() {
      return (
        <div className="goods-form-loading-spin">
          <Spin size="large" />
        </div>
      );
    }

    // 显示隐藏基础信息字段
    changeMoreSet(val: boolean) {
      this.setState({ moreSetting: val });
    }

    // XXX 做法/加料 FormRef
    /* eslint-disable @typescript-eslint/no-explicit-any */
    getAttrFormRefs(ref: any, idx: number) {
      this.attrForms[idx] = ref;
    }

    // 创建/编辑成功埋点
    submitSuccessMC() {
      const type = (this.props.match && this.props.match.params && this.props.match.params.type) || GoodsTypeEnum.SPU;
      const id = (this.props.match && this.props.match.params && this.props.match.params.id);
      const bidMap = {
        10: {
          save: 'b_eco_p7tg982a_mc',
          edit: 'b_eco_k7622oxr_mc',
          saveContinue: 'b_eco_rmbqy4s2_mc'
        },
        20: {
          save: 'b_eco_fr9omjuy_mc',
          edit: 'b_eco_rkr4wh00_mc',
          saveContinue: 'b_eco_x4qwvp7u_mc'
        },
      };
      const cidMap = {
        add_10: 'c_eco_ng0000008',
        add_20: 'c_eco_ng0000009',
        edit_10: 'c_eco_ng0000010',
        edit_20: 'c_eco_ng0000011',
      };
      const dishType = Number(type);
      const operationType = id ? 'edit' : this.continue ? 'saveContinue' : 'save';
      moduleClick(
        bidMap[dishType as 10][operationType],
        {},
        { cid: cidMap[`${operationType}_${dishType}` as 'add_10'] }
      );
    }

    componentWillUnmount() {
      // 页面卸载，清理编辑规格Store
      goodsSpecModel.reset();
      createEditModel.isReady = false;
      // 清空一下
      this.locationState = {};
      // 清空一下存储栈，页面初始化会重新生成recordInstance
      this?.recordInstance?.clear();
      const type = (this.props.match && this.props.match.params && this.props.match.params.type) || GoodsTypeEnum.SPU;
      const isCombo =  type !== GoodsTypeEnum.SPU;
      // 重置套餐校验规则
      if(isCombo){
        sellableStore.reset();
      }
    }

    modifyNameModal = async (v: any, typeText: string) => {
      const content = `变更${typeText}名称后，可能会影响对应的${typeText === '套餐' ? '套餐、' : ''}优惠活动、菜品券、报表统计等，请确认是否继续修改。`
      return new Promise((r) =>
        Modal.confirm({
          title: (
            <>
              您即将变更{typeText}名称为:{' '}
              <span style={{ color: 'red' }}>{v.name}</span>
            </>
          ),
          content,
          okText: '确认',
          cancelText: '取消',
          onOk: () => r(true),
          onCancel: () => r(false),
        })
      );
    }
    
    handleRef = (f: FormInstance) => {
      this.categoryForm = f
    }

    /** 异步校验校验是否存在问题, 存在问题时返回true */
    asyncCheckIsHasError = async (type: GoodsTypeEnum, formValue: IV, option?: {}) => {
      const { switchLatestStatusMap } = createEditModel;

      if (type === GoodsTypeEnum.SPU) {
        // 当[菜品库支持维护酒水类标品关联物品]打开, 且菜品选择管理库存时, 需校验菜品单位
        if (switchLatestStatusMap?.[SWITCH_KEY_ENUM.菜品库支持维护酒水类标品关联物品] && checkhasPermissionSync([PermissionEnum.SUPPLY_CHAIN, PermissionEnum.SUPPLY_NEW_CHAIN], true) && formValue?.manageInventory === ManageInventoryEnum.YES && checkUnitNeedWarning(toJS(unitModel.unitList), UnitBusinessEnum.酒水类, [formValue?.unitId])) {
          const isNext = await asyncConfirmModel(CHECK_UNIT_OPTION);
          if(!isNext) {
            return true;
          }
        }
      }
    }

    /**判断普通菜分组是否为空 */
    checkGoodsGroupIsEmpty = () => {
      const groupIds = createEditModel.goods.sideSpus?.map((i) => i.groupId);
      const isEmptyGoodsGroup = createEditModel.goods.sideSpuGroupConfigs?.some(
        (item) => !groupIds!.includes(item.groupId)
      );
      if (isEmptyGoodsGroup ) {
        // 菜品加料分组内容不可为空
        Modal.error({
          title: '提交失败',
          content: '菜品加料/配菜分组内容不可为空',
          okText: '我知道了',
        });
        return true;
      }
      return false;
    }

    /** 普通菜-品牌修改联动清空做法 */
    brandIdOnchange = () => {
      if (!createEditModel?.goods?.methods?.length) return;
      message.warn(CLEAR_METHODS_TEXT);
      createEditModel.resetAllMethods();
      // 做法表单数据流有问题，没有完全受控
      if (this.goodsForm.valueMap) {
        this.goodsForm.valueMap.allMethodMultimediasKV = {};
        Object.keys(this.goodsForm.valueMap).forEach(k => {
          if (k.includes('_method')) {
            delete this.goodsForm.valueMap[k];
          }
        })
      }
      // 做法表单数据流有问题，没有完全受控
      if (this.attrForms[0]?.groupForm?.valueMap) {
        this.attrForms[0].groupForm.valueMap = {};
      }
    }

    render() {
      const {
        clear, isChain, userService, Anchors, moreSetting, remarkNum, displaySet, showAnch, isSinglePoi,
      } = this.state;
      const { history } = this.props;
      const type = (this.props.match && this.props.match.params && this.props.match.params.type) || GoodsTypeEnum.SPU;
      const id = (this.props.match && this.props.match.params && this.props.match.params.id);
      const comboGroupTOS = createEditModel?.goods?.comboGroupTOS;
      const isSpu = Number(type) === GoodsTypeEnum.SPU;
      const departmentId = isChain && !isManagePoiConfig() ? createEditModel?.goods?.departmentTemplateId : createEditModel?.goods?.departmentOrgId;
      const typeText = isSpu ? '菜品' : '套餐';
      // 这里必须要改引用，不用你就等着被 formx 坑吧
      // formx 中的 value，必须包含 formX 中所有 formItem，所以这里主动拿到 valueMap
      const value = {
        ...(this.goodsForm ? this.goodsForm.valueMap || {} : {}),
        ...toJS(createEditModel.goods),
      };
      
      // 套餐分组中是否有非数字的分摊比例，如果有就不计算分摊价格
      let hasInvalidRate = false;
      const comboGroupPrice = comboGroupTOS?.slice().reduce((prev: number, current: ComboGroupTO & { percent: number }) => {
        if (current.type === ComboGroupTypeEnum.stable) {
          return plus(Number(prev), (Number(current?.skus?.reduce((p: number, c: Sku & { percent: number, type: GoodsTypeEnum }) => {
            if (Number.isNaN(Number(c?.percent))) {
              hasInvalidRate = true;
            }
            return plus(p, times(
              Number(c?.percent || 0), Number(c.maxAmount || 0),
            ))
          }, 0)) || 0));
        } else if (current.type === ComboGroupTypeEnum.custom) {
          if (Number.isNaN(Number(current?.percent))) {
            hasInvalidRate = true;
          }

          return plus(prev, Number(current?.percent || 0));
        }
        return 0;
      }, 0) || 0;
      const comboPriceDiff = minus(100, (comboGroupPrice || 0));
      if (!comboPriceDiff && createEditModel.triggerCalculate && !hasInvalidRate) {
        const skus = this.skusEditForm?.getFieldValue('skus') || [];
        createEditModel.calculateComboGpPrice(skus);
      }
      // @ts-ignore
      const settingSkuPrice = value.settingSkuPrice;
      const goodsFormWrapper = action === CreateOrEditRouteEnum.EDIT ? 'goods-form-edit-wrapper' : 'goods-form-create-wrapper';
      const goodsFormHide = goodsSpecModel.specEditVisible ? 'goods-form-hidden' : '';
      // 解决闭包问题, 这里不掉用isReady，啧SKU_TABLE不更新
      const isReady = createEditModel.isReady;
      return (
        <>
          <div
            className={`goods-form-wrapper ${goodsFormWrapper} ${goodsFormHide}`}
            id={this.props.id}
          >
            {/* 视角初始化前展示 loading */}
            {(clear === true || this.state.isChain === undefined) ? (
              this.renderLoading()
            ) : (
              <>
                <div id={this.containerId} className='goods-form-container' />
                <>
                  <FormPageCard
                    focusOnSubmitInvalid
                    loading={!!(id && !createEditModel.goods.id)}
                    bottom={
                      id ? null : (
                        <Button
                          type='primary'
                          className='save-continue-btn'
                          loading={this.state.loading}
                          onClick={this.onContinue}
                        >
                          保存并继续添加
                        </Button>
                      )
                    }
                    refForm={(ref) => {
                      if (ref) {
                        this.goodsForm = ref;
                      }
                    }}
                    // @ts-ignore
                    onSubmit={throttleFn(async (v: any) => {
                      this.setState({ loading: true });
                      this.currentTime = new Date();
                      const bidMap = {
                        10: {
                          save: 'b_eco_ng000037_mc',
                          edit: 'b_eco_ng000041_mc',
                        },
                        20: {
                          save: 'b_eco_ng000039_mc',
                          edit: 'b_eco_ng000043_mc',
                        },
                      };
                      const cidMap = {
                        //eslint-disable-next-line
                        add_10: 'c_eco_ng0000008',
                        //eslint-disable-next-line
                        add_20: 'c_eco_ng0000009',
                        //eslint-disable-next-line
                        edit_10: 'c_eco_ng0000010',
                        //eslint-disable-next-line
                        edit_20: 'c_eco_ng0000011',
                      };
                      const dishType = Number(type);
                      const operationType = id ? 'edit' : 'save';
                      moduleClick(
                        bidMap[dishType as 10][operationType],
                        {},
                        { cid: cidMap[`${operationType}_${dishType}` as 'add_10'] }
                      );
                      if (v.type === GoodsTypeEnum.SPU && !v.fractionalSaleType && createEditModel.initialFractionalSaleType) {
                        const skuIdList = createEditModel.goods.skus?.map((i) => i.id) || [];
                        const allSkuIds = skuIdList.filter((i) => i !== undefined);
                        // ts判断存在问题，上处代码已经将allSkuIds类型处理为number[]
                        const res = await (await getGoodsService()).getDishInCombo(allSkuIds as number[]);
                        if (res?.totalCount > 0) {
                          return Modal.confirm({
                            title: '提示',
                            content: '取消小数份售卖可能会导致所关联套餐无法正常销售，确定要取消吗？',
                            onOk: async () => {
                              if (
                                v.saleStatus === GoodsStatusEnum.TIMING &&
                                v?.saleTime.stopTime &&
                                v?.saleTime.stopTime.isSameOrBefore(this.currentTime)
                              ) {
                                //当前时间晚于停售时间，保存按照“停售状态”保存
                                Modal.confirm({
                                  title: '售卖状态确认',
                                  content: '您设置的停售时间已过，系统将按照已停售保存，是否确认保存？',
                                  okText: '确认',
                                  cancelText: '返回修改',
                                  onOk: async () => {
                                    await this.onSubmit(v, Number(type));
                                    this.setState({ loading: false });
                                  },
                                  onCancel: () => {
                                    this.setState({ loading: false });
                                  },
                                });
                              } else {
                                await this.onSubmit(v, Number(type));
                                this.setState({ loading: false });
                              }
                            },
                            onCancel: () => {
                              this.setState({ loading: false });
                            },
                          });
                        }
                      }
                      if(v.saleStatus === GoodsStatusEnum.TIMING && v?.saleTime.stopTime && v?.saleTime.stopTime.isSameOrBefore(this.currentTime)) { //当前时间晚于停售时间，保存按照“停售状态”保存
                        Modal.confirm({
                          title: '售卖状态确认',
                          content: '您设置的停售时间已过，系统将按照已停售保存，是否确认保存？',
                          okText: '确认',
                          cancelText: '返回修改',
                          onOk: async () => {
                            await this.onSubmit(v, Number(type));
                            this.setState({ loading: false });
                          },
                          onCancel: () => {
                            this.setState({ loading: false });
                          },
                        })
                      } else {
                        await this.onSubmit(v, Number(type));
                        this.setState({ loading: false });
                      }
                    }, 1000)}
                      value={value}
                    // @ts-ignore
                    onChange={(val: any, key) => {
                      this.formOnChange(val, key, !!id, type);
                    }}
                    title={
                      <>
                        <div className={isSpu ? 'card-title' : 'card-header'}>
                          <div className='title-text'>
                            <Icon
                              type='arrow-left'
                              style={{ marginRight: '8px', cursor: 'pointer' }}
                              onClick={() => this.navigateToGoodsList()}
                            />
                            {id && action === CreateOrEditRouteEnum.EDIT ? '编辑' : '创建'}
                            {typeText}
                          </div>
                          {!isSpu && (
                            <div className='combo-price-tips'>
                              <>
                                {settingSkuPrice === ComboSettingSkuPriceEnum.manual
                                  ? '所有固定分组&可选分组的菜品分摊比率总和，必须等于100%。'
                                  : '根据套餐售卖价和菜品原价，自动计算出套餐子菜品的单价'}
                                {settingSkuPrice === ComboSettingSkuPriceEnum.manual
                                  && comboPriceDiff !== 0 && (
                                    <span className='combo-price-text'>
                                      当前分摊比率总计: {comboGroupPrice} %， 差额:{' '}
                                      <span style={{ color: 'red' }}>{comboPriceDiff}%</span>
                                    </span>
                                  )}
                              </>
                            </div>
                          )}
                        </div>
                        {value.publishType
                        && !isChain
                        && !isManagePoiConfig()
                        && action !== CreateOrEditRouteEnum.COPY
                        && createEditModel.goods.fieldControl?.controlType === 2 ? (
                          <Alert
                            type='warning'
                            message='由于集团管控权限设置，以下部分字段不可编辑'
                            closable
                          />
                        ) : null}
                        {showAnch && (
                          <div className='anchor-list'>
                            <Anchor
                              affix={false}
                              offsetTop={40}
                              getContainer={() => {
                                const containerWrapper = document.querySelector(
                                  `.${goodsFormWrapper} .ant-PageCard-body `
                                ) as HTMLElement;
                                return containerWrapper;
                              }}
                              onClick={(e) => {
                                e.preventDefault();
                              }}
                            >
                              {Anchors.length > 0
                                ? Anchors.map((item, index) => (
                                    <>
                                      <Link
                                        key={item.anchor}
                                        href={`#${item.anchor}`}
                                        title={item.name}
                                      >
                                        <div className='active-link' />
                                      </Link>
                                    </>
                                  ))
                                : ''}
                            </Anchor>
                          </div>
                        )}
                      </>
                    }
                    cancelButtonProps={{
                      onClick: () => this.navigateToGoodsList(),
                    }}
                    okButtonProps={{
                      disabled: this.state.isSubmitButtonDisabled,
                      loading: this.state.loading,
                      onClick: () => {
                        ManualCateforyPostion(createEditModel.goods);
                        this.goodsForm && this.goodsForm.submit();
                      },
                      /**
                       * 页面confirm按钮自定义背景色与border
                       * 后续将FormPageCard替换给规范组件
                       */
                      style: {
                        backgroundColor: '#FFD100',
                        borderWidth: 0,
                      }
                    }}
                    absolute
                  >
                    {({
                      canWeight,
                      saleStatus,
                      saleTime,
                      departmentInherit,
                      taxRateInherit,
                      taxRateUuid,
                    }) => [
                      (
                      // 该提示下线时间：小程序版本上线两个后
                      createEditModel?.switchLatestStatusMap?.[
                        SWITCH_KEY_ENUM.SPECIFICATION_PICTURE_SETTING
                      ] && isSpu && <Alert
                          type='warning'
                          showIcon
                          message='菜品不同规格设置不同图片需配合小程序 v5.41.04 版本（2023年6月8日上线）及以上版本使用，如需使用请确保小程序已升级。'
                        />
                      ),
                      // 5.46.10套餐可选分组支持部分菜品复选
                      (createEditModel?.switchLatestStatusMap?.[SWITCH_KEY_ENUM.设置可选套餐子菜限购份数] && !isSpu && (
                        <CustomTxtVersionControl version='5.46.10'>
                        <Alert
                          type='warning'
                          showIcon
                          message={
                            <>
                            {isChain && !isManagePoiConfig() ? (
                              <>
                                <span>
                                  可选套餐设置子菜限购份数需要配合收银机v5.46.10版本及以上使用
                                </span>
                                <CheckModalTxt version='5.46.10' />
                              </>
                            ) : (
                              '可选套餐设置子菜限购份数需要配合收银机v5.46.10版本及以上使用，请尽快升级收银版本。'
                            )}
                          </>
                          }
                        />
                      </CustomTxtVersionControl>
                      )),
                    !isSpu && <Alert
                      type='warning'
                      showIcon
                      message={
                        <>
                          若未添加套餐菜品，或套餐菜品部分有影响套餐销售的灰色提示信息未修改正确，允许保存套餐，但不允许操作启售。
                        </>
                      } />,
                      /* 组件库问题，子表单提前初始化就有问题，必须等数据加载完成 */
                      !id || createEditModel.goods.id ? (
                        <BasicInfoPart1
                          typeText={typeText}
                          action={action}
                          brandId={createEditModel.goods.brandId}
                          type={type}
                          id={id}
                          history={history}
                          canWeight={canWeight}
                          isCopy={action === CreateOrEditRouteEnum.COPY}
                          isChain={isChain}
                          isBranchPoi={userService?.isBranchPoi()}
                          handleRef={this.handleRef}
                          channelCategoryForm={this.categoryForm}
                          brandIdOnchange={this.brandIdOnchange}
                        />
                      ) : null,
                      /* 组件库问题，子表单提前初始化就有问题，必须等数据加载完成 */
                      isReady && userService && (!id || createEditModel.goods.id) ? (
                        <div className='goods-standard-container'>
                          <div className="goods-standard-container_label">{isSpu ? '菜品规格：' : '套餐价格：'}</div>
                          <SkusEdit
                            type={Number(type)}
                            userService={userService}
                            isEdit={!!id}
                            chargeType={this.state.boxChargeType}
                            action={action}
                            history={history}
                            ref={(ref) => {
                              if (ref) {
                                this.skusEditForm = ref.formRef;
                                createEditModel.skusEditForm = ref.formRef;
                              }
                            }}
                          />
                        </div>
                      ) : < Spin />,
                      // <Validator name="newSkus" label={`${typeText}规格`} required>
                      //   <SkusEdit></SkusEdit>
                      // </Validator>,
                      /* 组件库问题，子表单提前初始化就有问题，必须等数据加载完成 */
                      !id || createEditModel.goods.id ? (
                        <BasicInfoPart2
                          brandId={createEditModel.goods.brandId}
                          typeText={typeText}
                          type={type}
                          id={id}
                          history={history}
                          isCopy={action === CreateOrEditRouteEnum.COPY}
                          canWeight={canWeight}
                          moreSetting={moreSetting}
                          remarkNum={remarkNum}
                          changeMoreSet={(sets) => {
                            this.changeMoreSet(sets);
                          }}
                          changeRemarkNum={(val) => {
                            this.setState({ remarkNum: val });
                          }}
                          chargeType={this.state.boxChargeType}
                          action={action}
                          isSinglePoi={isSinglePoi}
                          isChain={isChain}
                          isBranchPoi={userService?.isBranchPoi()}
                          goodsForm={this.goodsForm}
                        />
                      ) : null,
                      /* 做法加料 */
                      isSpu && (!id || createEditModel.goods.id) ? (
                        <Attribute
                          getRef={(ref, idx) => {
                            this.getAttrFormRefs(ref, idx);
                          }}
                          type={type}
                          history={history}
                          containerId={this.containerId}
                          action={action}
                          goodsForm={this.goodsForm}
                          isChainPage={userService && userService.isHeadOffice() && !isManagePoiConfig()}
                        />
                      ) : null,
                      /* 分组设置 */
                      isSpu ? null : (
                        <div className='combo-group-container' id='ComboDish'>
                          {isSpu ? null : (
                            <GroupSelect
                              action={action}
                              goodsForm={this.goodsForm}
                              settingSkuPrice={settingSkuPrice}
                              setSubmitDisabled={this.setSubmitDisabled}
                              ref={(ref: { form: { form: FormX }[] }) => {
                                if (ref) {
                                  this.groupFrom = ref;
                                }
                              }}
                            />
                          )}
                        </div>
                      ),
                      /* 打印信息 */
                      createEditModel.printConfigs ? (
                        <PrintConfig isSpu={isSpu} history={history} isChain={isChain} action={action} />
                      ) : null,
                      /* 销售信息 */
                      !id || createEditModel.goods.id ? (
                        <SaleInfo
                          isChain={isChain && !isManagePoiConfig()}
                          saleStatus={saleStatus}
                          typeText={typeText}
                          canWeight={canWeight}
                          type={type}
                          action={action}
                          saleTime={saleTime}
                          handleRef={(f) => {
                            this.statusForm = f
                          }}
                        />
                      ) : null,
                      /* 财务设置 */
                      isSpu ? (
                        <FinanceInfo
                          userService={userService}
                          matchQuery={this.props.matchQuery}
                          departmentInherit={departmentInherit}
                          taxRateInherit={taxRateInherit}
                          departmentId={departmentId}
                          action={action}
                        />
                      ) : null,
                      /** 组件库问题，子表单提前初始化就有问题，必须等数据加载完成 */
                      /* 展示信息 */
                      !id || createEditModel.goods.id ? (
                        <DisplayInfo
                          isChain={isChain && !isManagePoiConfig()}
                          type={+type}
                          history={history}
                          displaySet={displaySet}
                          changeDisyplaySet={(val) => {
                            this.setState({ displaySet: val });
                          }}
                          action={action}
                        />) : null,
                    ]}
                  </FormPageCard>
                </>
              </>
            )}
          </div>
        </>
      );
    }
  }

  if (action === CreateOrEditRouteEnum.CREATE) {
    return getRenderTimeOfComponent(
      (props: IProps) => <CreateOrEdit {...props} />,
      { pageName: PAGE.GOODS_CREAT, }
    ) as unknown as typeof CreateOrEdit;
  }
  if (action === CreateOrEditRouteEnum.EDIT) {
    return getRenderTimeOfComponent(
      (props: IProps) => <CreateOrEdit {...props} />,
      { pageName: PAGE.GOODS_EDIT, }
    ) as unknown as typeof CreateOrEdit;
  }
  return CreateOrEdit;
};

export default getCreateOrEdit(CreateOrEditRouteEnum.COMPONENT);
