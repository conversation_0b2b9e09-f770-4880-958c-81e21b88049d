import { action, observable, computed, toJS } from 'mobx';
import moment from 'moment';
import _ from 'lodash';
import { doAction } from '@mtfe/next-biz/es/lib/actions';
import { checkhasPermissionSync } from '@mtfe/next-biz/es/services/permission';
import { FormInstance, Modal, message } from '@mtfe/sjst-antdx-saas';

import {
  goodsPublishOptions,
  wmDishAdvanceInfoOpts,
  wmDishBasicOpts,
  wmDishCategoryOpts,
  wmDishDetailOpts,
  FormInfo,
  CheckBoxFieldEnum,
  updateMode,
  WmTemplateTypeEnum,
  IssueModeEnum,
  entryRangeTypeEnum,
  WMChannelEnum,
  PkgPubTabsKey,
  outerPreviewFieldOpts,
  taskEffectiveTypeOpts,
  InitMoreFields,
  tabOptions,
  MTwmDishSaleOpts,
  ELEwmDishSaleOpts,
  PublishItemTypeEnum,
  CategoryPropertyEnum,
  categoryOuterPreviewFieldOpts,
  WmCustomMoreFields,
} from '../../pages/GoodsPublish/WaimaiPkgPub/constant';
import WaimaiService from '@mtfe/next-biz/es/services/waimai';
import { brand } from '@rms-goods/root/src/models/brand';
import { WaimaiSourceEnum } from '@rms-goods/root/src/services/waimai';
import { getService } from '@mtfe/next-biz/src/services/user';
import { Template } from '@rms-goods/root/src/components/TemplateListSelector';
import { ABtestEnum, isAB } from '@rms-goods/root/src/components/AB';
import type { OldRootObject as PoiInfo } from '@typings/NetTypings/responses/POST/api/v1/pois/query-by-poiids';
import { batchGetSwitchConfig } from '@mtfe/next-biz/es/services/config';
import { getSwitchConfigMap } from '@rms-goods/root/src/utils/goods/getSwitchConfigMap';
import { SWITCH_KEY, SWITCH_KEY_ENUM } from '@typings/GoodsTypings/switch';
import templateService from '@rms-goods/services/goodsTemplate';
import { transformTemplateRelationType } from '../../pages/GoodsPublish/WaimaiPkgPub/Publish/Common';
import {
  fetchIssueOverview,
  batchCreateTemplate,
  BatchCreateReq,
  IssueSnapshotReq,
  creatIssueSnapshot,
  fetchPkgPublishCopy,
  IssueLimitConfig,
  getIssueNumLimitConfig,
  IssueLimitConfigType,
  IPostPublishMethods,
} from '@rms-goods/services/goodsPublish';
import { fetchPoisInfoByIds } from '@rms-goods/services/pois';
import {
  checkVirtual,
  getOrgsWithVirtualPoi,
  getOrgId,
  getOrgCode,
} from '@rms-goods/utils/poiVirtual';
import { TemplateTypeEnum, ColumnSettingEnum, WmSource, TaxRateSwitchStatusEnum } from '@typings/GoodsTypings/enum';
import {
  DeliverTypeEnum,
  fetchDefaultField,
  saveDefaultMoreField,
  IWmPublishMethods,
  IssueTypeEnum,
  checkIssueWithBackup,
  WaiMaiPublishRules,
} from '@rms-goods/services/goodsPublish';
import { historyListColums } from '../../pages/GoodsPublish/Common/columns';
import { createWaimaiModalSelector } from '@mtfe/next-biz/es/components/Waimai/BrandSpu';
import { SelectorType as WmSelectorType } from '@mtfe/next-biz/src/components/Waimai/types';

import { PrintRuleEnum, wmSourceEnum } from '../../pages/GoodsPublish/Waimai/constant';
import { WaimaiSkuItem } from '../../services/waimaiTemplate';
import { onFieldSetting } from '../../pages/GoodsPublish/WaimaiPkgPub/Common/utils';
import { OuterUpdateFieldsInfo, UniversalBrand, initCheckedMap, initFormRuleFieldsValue, initIndeterminateMap, CategoryTO, categoryFormRuleFieldsValue, transformCategoryDate, InitFormRuleFields } from './helper';
import { categoryDataModule } from '../../pages/GoodsPublish/WaimaiPkgPub/History/const';
import { getBatchSwitchMoreItemKeyConfigMapV2, SwitchMoreItemKeyMap, getSwitchConfigMapV1 } from '@rms-goods/root/src/utils/goods/getSwitchConfigMap';
import { taxAndDepartmentModel } from '../goods';
import { VERSION_NUMBER } from '../../utils/versions';

interface IPoiInfo extends PoiInfo {
  poiId: number;
  orgId?: number;
  id?: number;
  no?: number;
  orgCode?: number;
  brandName?: string;
  mainPoiId?: number;
  mainOrgCode?: string;
  virtualShop?: boolean;
}

export interface ITablePoiInfo extends IPoiInfo {
  ifExpired?: boolean;
  menuEffectiveTime?: number;
}

/** 门店所有信息 */
const poiInfoCache: Map<number, IPoiInfo> = new Map();

export type FieldEnumMap = {
  [key in CheckBoxFieldEnum]?: boolean;
};

class WMPkgPublish {
  /** 页面loading */
  @observable
  loading: boolean = false;

  /** 自定义字段默认参数弹窗loading */
  @observable
  defaultMoreFieldsLoading: boolean = false;

  /** 所有子tab是否有权限 */
  @observable
  goodsPublishHasAllTabPermission: boolean = true;

  @observable
  activeTabKey: PkgPubTabsKey = PkgPubTabsKey.菜品下发;

  /** 下发方式 */
  @observable
  issueMode: IssueModeEnum = IssueModeEnum.按模板下发菜品;

  /** 选择的平台 */
  @observable
  wmChannel: WMChannelEnum = WMChannelEnum.MT;

  /** 菜单模板 */
  @observable
  templateList: Template[] = [];

  /** 模板菜品 */
  @observable
  tempGoods: IV[] = [];

  /** 菜品库菜品 */
  @observable
  goods: IV[] = [];

  /** 菜品分类 */
  @observable
  categoryList: CategoryTO[] = [];

  /** 适用门店 */
  @observable
  poiList: ITablePoiInfo[] = [];

  @observable
  switchStatusMap: { [index: string]: boolean } = {};

  @observable // 高频字段可编辑
  canEditTemplateFieldSwitchMap: {[k: string]: boolean | undefined} = {};

  @observable
  poiLoading: boolean = false;

  @observable
  tempTableLoading: boolean = false;

  @observable
  goodsLoading: boolean = false;

  /** 下发规则表单信息 */
  @observable
  formInfo: FormInfo[] = [];

  /** 下发规则表单ref */
  @observable
  publishRuleFormRef?: FormInstance | null;

  /** 设置更多字段表单ref */
  @observable
  moreFieldsFormRef?: FormInstance | null;

  /** 自定义更新字段设置默认参数表单ref */
  @observable
  moreDefaultFieldsFormRef?: FormInstance | null;

  /** 更多字段默认值 */
  @observable
  initialMoreFieldsValue: InitMoreFields = this.getInitialMoreFieldsValue();

  /** 更多字段多选框半选样式 */
  @observable
  indeterminateMap: FieldEnumMap = initIndeterminateMap;

  /** 设置默认参数多选框半选状态 */
  @observable
  defaultIndeterminateMap: FieldEnumMap = initIndeterminateMap;

  /**更多字段多选框选择状态 */
  @observable
  checkedMap: FieldEnumMap = initCheckedMap;

  /** 设置默认参数多选框选择状态 */
  @observable
  defaultCheckedMap: FieldEnumMap = initCheckedMap;

  /** 下发规则默认值 */
  @observable
  initialWmPublishRules: InitFormRuleFields = initFormRuleFieldsValue;

  /** 模板id */
  @observable
  templateId: string[] = [];

  /** 下发数据快照标识id */
  @observable
  issueUniqueId?: number;

  /** 下发菜品库菜品品牌id */
  @observable
  brandId?: number;

  /** 复制信息 */
  @observable
  copyDetail?: IV;

  /** 任务详情taskId */
  @observable
  taskId?: number;

  @observable
  abStrategyMap = { [ABtestEnum.味千]: false };

  /** 下发失败任务重试 */
  @observable
  retry: boolean = false;

  @observable
  historyListCols = historyListColums;
  
  /** 存储下发字段参数，便于二次确认预览 */
  @observable
  savePublishParams?: IWmPublishMethods;

  @observable
  issueLimitNumConfig?: IssueLimitConfig;

  /** 是否填写数据 */
  @computed
  get isFillData() {
    return (
      !!this.templateList.length ||
      !!this.poiList.length ||
      !!this.tempGoods.length ||
      !!this.goods.length ||
      !!this.categoryList.length
    );
  }

  @computed
  get wmChannelName() {
    return this.wmChannel === WMChannelEnum.MT ? '美团' : '饿了么';
  }

  /** 价格方案是否需要展示 */
  @computed
  get priceSchemeHide() {
    return (
      !this.switchStatusMap[SWITCH_KEY_ENUM.价格方案适用门店] &&
      !this.switchStatusMap[SWITCH_KEY_ENUM.管控方案分渠道管理]
    );
  }

  /**
   * 外卖菜品选择器，监听 wmSource 的变化
   * 否则页面中每次 state 变化都会导致重新创建组件，无法实现 confirmLoading 的效果
   */
  @computed
  get waimaiGoodsSelector() {
    const wmSource =
      this.wmChannel === WMChannelEnum.MT
        ? wmSourceEnum.美团外卖
        : wmSourceEnum.饿了么外卖;
    //@ts-ignore
    return createWaimaiModalSelector(+wmSource, WmSelectorType.Sku, true, true, true, true);
  };

  @computed
  get isCategoryPublish() {
    return this.issueMode === IssueModeEnum.分类下发;
  };

  @action
  initMoreFieldsValue() {
    this.initialMoreFieldsValue = this.getInitialMoreFieldsValue();
  }
  
  /** 切换下发方式或者只有分类下发的权限时重新获取下发规则的默认值，以便分类下发的默认值正确 */
  @action
  resetInitialWmPublishRules() {
    this.initialWmPublishRules = this.isCategoryPublish ? categoryFormRuleFieldsValue : initFormRuleFieldsValue;    
  }

  /** 获取菜品下发下有权限的tab key */
  @action
  options() {
    const opt = tabOptions.filter((tab) => checkhasPermissionSync(tab.code));
    if (!opt.length) {
      this.goodsPublishHasAllTabPermission = false;
    }
    return opt;
  }

  /** 下发方式重置 */
  @action
  initIssueMode() {
    this.issueMode = this.options()?.[0]?.tabKey;
  }

  @action
  setPublishRuleFormRef(ref: FormInstance | null) {
    this.publishRuleFormRef = ref;
  }

  @action
  setMoreFieldsFormRef(ref: FormInstance | null) {
    this.moreFieldsFormRef = ref;
  }

  @action
  setMoreDefaultFieldsFormRef(ref: FormInstance | null) {
    this.moreDefaultFieldsFormRef = ref;
  }

  /** 设置弹窗内form默认值 */
  @action
  setMoreFieldValue(field: string[], value: any) {
    // @ts-ignore
    this.initialMoreFieldsValue[field[0]][field[1]] = value;
  }

  @action
  setActiveTabKey(key: PkgPubTabsKey) {
    this.activeTabKey = key;
  }

  @action
  setABStrategyMap(ab: { [k: string]: boolean }) {
    this.abStrategyMap = { ...this.abStrategyMap, ...ab };
  }

  @action
  setTemplateList(templateList: Template[]) {
    this.templateList = templateList;
  }
  
  @action
  setTempGoods(goods: IV[]) {
    this.tempGoods = goods;
  }

  @action
  setGoods(goods: IV[]) {
    this.goods = goods;
  }

  @action
  setCategoryList(categoryList: CategoryTO[]) {
    this.categoryList = categoryList;
  }

  @action
  setCheckedMap(checkedMap: FieldEnumMap, isDefaultSetting?: boolean) {
    if(isDefaultSetting) {
      this.defaultCheckedMap = checkedMap;
    }else {
      this.checkedMap = checkedMap;
    }
  }

  @action
  setDefaultCheckedMap(checkedMap: FieldEnumMap) {
    this.defaultCheckedMap = checkedMap;
  }

  @action
  setIndeterminateMap(indeterminateMap: FieldEnumMap, isDefaultSetting?: boolean) {
    if(isDefaultSetting) {
      this.defaultIndeterminateMap = indeterminateMap;
    }else {
      this.indeterminateMap = indeterminateMap;
    }
  }

  @action
  setDefaultIndeterminateMap(indeterminateMap: FieldEnumMap) {
    this.defaultIndeterminateMap = indeterminateMap;
  }

  @action
  cleanTemplateList() {
    this.templateList = [];
  }

  @action
  cleanPoiList() {
    this.poiList = [];
  }

  @action
  cleanTempGoods() {
    this.tempGoods = [];
  }

  @action
  cleanGoods() {
    this.goods = [];
  }

  @action
  cleanCategoryList() {
    this.categoryList = [];
  }

  /** 下发历史列表字段设置 */
  @action
  async onColumnsSetting(showModal: boolean) {
    const currentColumns = await onFieldSetting(showModal, historyListColums, ColumnSettingEnum.外卖整包下发历史);
    this.historyListCols = currentColumns;
  }

  @action
  // 重置 更新字段范围相关数据
  resetPublishUpdateFieldData() {
    const outerPreviewField = this.issueMode === IssueModeEnum.分类下发 ? categoryOuterPreviewFieldOpts : outerPreviewFieldOpts;
    // 重置自定义更新字段form默认值及重置表单
    const initialMoreFieldsValue = this.getInitialMoreFieldsValue();

    this.initialMoreFieldsValue = initialMoreFieldsValue;
    this.moreFieldsFormRef?.setFieldsValue(initialMoreFieldsValue);
    this.publishRuleFormRef?.setFieldValue('outerPreviewFields', outerPreviewField?.map(opt => opt.code));
    this.resetPublishFormDepsData();
  }

  /** 重置下发规则相关form */
  @action
  resetPublishRulesFormData() {
    this.publishRuleFormRef?.resetFields();
    this.resetPublishUpdateFieldData();
  }

  /** 重置下发规则相关form的实例 */
  @action
  resetPublishRulesFormRef() {
    this.publishRuleFormRef = null;
    this.moreFieldsFormRef = null;
    this.resetPublishFormDepsData();
  }

  /** 重置下发规则相关表单数据外的其他数据： */
  @action
  resetDataButPublishForm() {
    this.cleanTemplateList();
    this.cleanPoiList();
    this.cleanTempGoods();
    this.cleanGoods();
    this.cleanCategoryList();
    this.templateId = [];
    this.issueUniqueId = undefined;
  }

  /** 切换顶部菜单 菜品下发 <=> 下发历史 */
  @action
  changeMenu = () => {
    this.resetDataButPublishForm();
    this.resetPublishRulesFormData();
    this.resetPublishRulesFormRef();
    this.wmChannel = WMChannelEnum.MT;
  };

  /** 重置下发规则表单依赖的外部数据 */
  @action
  resetPublishFormDepsData() {
    this.checkedMap = initCheckedMap;
    this.indeterminateMap = initIndeterminateMap;
  }

  @action
  changeIssueMode(mode: IssueModeEnum) {
    this.issueMode = mode;
  }

  @action
  changeRetry(retry: boolean) {
    this.retry = retry;
  }

  @action
  changeWmChannel(channel: WMChannelEnum) {
    this.wmChannel = channel;
  }

  @action
  async fetchIsWq() {
    const wq = await isAB(ABtestEnum.味千);
    this.setABStrategyMap({ [ABtestEnum.味千]: !!wq });
  }

  /** 获取品牌 */
  @action
  async getBrand() {
    const brandList = await brand.getBrandList();
    const filterNormalBrand = toJS(brandList)?.filter(
      (brand) => brand.brandId !== UniversalBrand.brandId
    );
    this.brandId = filterNormalBrand?.[0].brandId;
    return filterNormalBrand || [];
  }

  /** 切换品牌 */
  @action
  changeBrandId(id: number) {
    this.brandId = id;
  }

  /** 重置品牌 */
  @action
  async initBrandId() {
    const brand = await this.getBrand();
    this.brandId = brand?.[0]?.brandId;
  }

  /**
   * 开关
   * 价格方案开关
   * 外卖收银套餐开关
   */
  @action
  async querySwitchStatus() {
    this.loading = true;
    const [switchStatus] = await Promise.all([
      batchGetSwitchConfig([
        SWITCH_KEY[SWITCH_KEY_ENUM.价格方案适用门店],
        SWITCH_KEY[SWITCH_KEY_ENUM.管控方案分渠道管理],
        SWITCH_KEY[SWITCH_KEY_ENUM.美团收银套餐],
        SWITCH_KEY[SWITCH_KEY_ENUM.外卖菜单模板套餐子菜联动展示],
        SWITCH_KEY[SWITCH_KEY_ENUM.外卖菜单模板可编辑字段管理],
      ]),
      taxAndDepartmentModel.getTaxAndRate()
    ]).catch((error) => {
      this.loading = false;
      throw error;
    });
    if (switchStatus) {
      this.switchStatusMap = getSwitchConfigMap(switchStatus);
      const r = getSwitchConfigMapV1(switchStatus);
      const canEditTemplateFieldSwitchMap = getBatchSwitchMoreItemKeyConfigMapV2(r?.[SWITCH_KEY_ENUM.外卖菜单模板可编辑字段管理] as SwitchMoreItemKeyMap);
      this.canEditTemplateFieldSwitchMap = canEditTemplateFieldSwitchMap;
    }
    this.loading = false;
  }

  /** 查询自定义更新字段默认参数 */
  @action
  async getDefaultMoreFieldsValue() {
    this.defaultMoreFieldsLoading = true;
    const params = {
      publishType: PublishItemTypeEnum.外卖,
      wmSource: this.wmChannel === WMChannelEnum.MT
      ? wmSourceEnum.美团外卖
      : wmSourceEnum.饿了么外卖,
    }
    const res = await fetchDefaultField(params).catch((err) => {
      this.defaultMoreFieldsLoading = false;
      throw err;
    });

    if(res.dish) {
      let defaultMorevalue: Partial<InitMoreFields> = {};
      Object.keys(res).forEach(key => {
        // 选择【全部更新】或者【全部不更新】相当于其余字段全部勾选
        if(res[key].mode !== updateMode.部分更新) {
          defaultMorevalue = {
            ...defaultMorevalue,
            [key]: { mode: res[key].mode },
          };
        }else {
          defaultMorevalue = {
            ...defaultMorevalue,
            [key]: res[key],
          }
        }
      });
      
      if (defaultMorevalue?.dish?.advancedFields) {
        const initialAdvanceFieldCodes = this.getInitAdvanceFields().map(item => item.code);
        // 如果税率开关关闭，去掉税目, 
        defaultMorevalue.dish.advancedFields = defaultMorevalue.dish.advancedFields.filter(code => initialAdvanceFieldCodes.includes(code));
      }
      
      const modalFormValue = this.transformToFormValues(defaultMorevalue);
      const resultDefaultFieldsValue = _.omit(modalFormValue, 'outerPreviewFields');
      this.handleCheckBoxStatus(defaultMorevalue, true);
      this.moreDefaultFieldsFormRef?.setFieldsValue(resultDefaultFieldsValue);
      this.defaultMoreFieldsLoading = false;
      
      return modalFormValue;
    };

    // 若接口数据中dish没有值，则表示该商户未设置过默认参数，需默认为【全部更新】
    this.moreDefaultFieldsFormRef?.setFieldsValue(this.getInitialMoreFieldsValue());
    this.handleCheckBoxStatus(this.getInitialMoreFieldsValue(), true);
    this.defaultMoreFieldsLoading = false;
  }

  @action
  async saveDefaultMoreFieldsValue() {
    const defaultMoreFieldsValue = this.moreDefaultFieldsFormRef?.getFieldsValue() || this.getInitialMoreFieldsValue();
    let finalDefaultFieldsValue = {};
    Object.keys(defaultMoreFieldsValue).forEach(key => {
      // 选择【全部更新】或者【全部不更新】只需要传mode
      if(defaultMoreFieldsValue[key].mode !== updateMode.部分更新) {
        finalDefaultFieldsValue = {
          ...finalDefaultFieldsValue,
          [key]: _.pick(defaultMoreFieldsValue[key], 'mode'),
        }
      }else {
        finalDefaultFieldsValue = {
          ...finalDefaultFieldsValue,
          [key]: defaultMoreFieldsValue[key],
        }
      }
     });
     
    const params = {
      publishType: PublishItemTypeEnum.外卖,
      wmSource: this.wmChannel === WMChannelEnum.MT
      ? wmSourceEnum.美团外卖
      : wmSourceEnum.饿了么外卖,
      wmIssuedDefaultField: finalDefaultFieldsValue,
    };

    const res = await saveDefaultMoreField(params).catch((err) => {      
      message.error(`设置默认参数失败，原因：${err?.message}`);
    });
    
    if(res.result) {
      this.moreDefaultFieldsFormRef?.setFieldsValue(defaultMoreFieldsValue);
      message.success('设置默认参数成功');
    }
  }

  // 获取门店详细信息，如orgId
  @action
  async getPoiInfo(poiIds: number[], wmSource?: WmSource) {
    if (!poiIds.length) {
      return [];
    }
    let data;
    if (checkVirtual()) {
      const res = await getOrgsWithVirtualPoi(wmSource);
      // 通过虚拟门店接口拉到的数据为全量数据 需要通过当前已选poiIds过滤&虚拟门店为poiid为virPoiId需要手动替换为poiId
      data = res?.filter?.((i: IV) => i.poiId && poiIds.includes(i.poiId));
    } else {
      data = await fetchPoisInfoByIds(poiIds);
    }
    data.forEach((poi: IPoiInfo) => {
      if (poi.poiId) {
        poiInfoCache.set(poi.poiId, poi);
      }
    });
    return data;
  }

  /** 获取下发门店信息,在门店选择器提供的信息之外，需要额外补充价格方案信息 */
  @action
  async queryPoiList(poiIds: number[], isCopy?: boolean) {
    this.poiLoading = true;
    await this.getPoiInfo(
      poiIds,
      this.wmChannel === WMChannelEnum.MT ? WmSource.MT : WmSource.ELE
    ).catch((e) => {
      this.poiLoading = false;
      message.error(e?.message);
      // eslint-disable-next-line
      throw(e?.message);
    });
    const newPois: IPoiInfo[] = poiIds.map((id) => {
      const poi = poiInfoCache.get(id);
      if (poi && poi.poiId) {
        return poi;
      }
      return {
        poiId: id,
      };
    });
    
    const issueData = !this.priceSchemeHide && !this.isCategoryPublish && await fetchIssueOverview(poiIds, this.wmChannel, false).catch((e) => {
      this.poiLoading = false;
      message.error(e?.message);
    });
    const poiDetails = newPois.map((item) => {
      const poiId = item?.poiId;
      const issueDetail = issueData ? issueData?.find((issue) => issue.poiId === poiId) : {};
      return { ...item, ...issueDetail };
    });
        
    if(!poiDetails?.length && isCopy) {
      Modal.warning({
        title: '复制失败',
        content: '当前无可用门店，请在本页重新选择门店数据后下发。',
        cancelButtonProps: { hidden: true },
      });
    };
    //@ts-ignore
    this.poiList = poiDetails;
    this.poiLoading = false;
  }

  @computed
  get getTemplateType() {
    if(this.isCategoryPublish) {
      return this.wmChannel === WMChannelEnum.MT ? WmTemplateTypeEnum.美团分类下发 : WmTemplateTypeEnum.饿了么分类下发;
    }else {
      return this.wmChannel === WMChannelEnum.MT ? WmTemplateTypeEnum.美团 : WmTemplateTypeEnum.饿了么;
    };
  }

  /** 创建模板快照 */
  @action
  async batchCreateTemplate() {
    this.loading = true;
    const req: BatchCreateReq = {
      templateType: this.getTemplateType,
      itemTemplateReqs: [],
    };
    
    const commonItemTemplateReq = {
      templateType: this.getTemplateType,
      // 如果选择了多个模板匹配对应模板的门店,否则就取门店选择器的门店
      refPoiList: this.poiList?.map((poi) => ({
        poiId: poi?.poiId,
        poiName: poi?.poiName,
        orgCode: getOrgCode(poi),
        brandId: poi?.brandId,
        brandName: poi?.brandName,
        merchantNo: poi?.merchantNo,
      })),
    };

    // 按模板下发
    if (this.issueMode === IssueModeEnum.按模板下发菜品) {
      const tempLen = this.templateList.length;
      req.itemTemplateReqs = this.templateList!.map((t) => {
        const { brandId, templateName, id, appliedChannels = [] } = t;
        // 按模板下发如果选择的模板数大于1个,页面上不支持选门店,只能下发模板关联对应的门店.
        const refPoiList = () => {
          if (tempLen > 1) {
            const pois = this.poiList
              ?.filter((poi) =>
                t.refPoiList?.find((p: { poiId: number }) => p.poiId === poi.poiId)
              );
            return pois;
          }
          return this.poiList;
        };
        return {
          ...commonItemTemplateReq,
          brandId,
          // 模板名称+时间戳
          templateName: templateName + '_' + moment().valueOf(),
          relTemplateId: id,
          refPoiList: refPoiList()?.map(poi => ({
            poiId: poi?.poiId,
            poiName: poi?.poiName,
            orgCode: getOrgCode(poi!),
            brandId: poi?.brandId,
            brandName: poi?.brandName,
            merchantNo: poi?.merchantNo,
          })),
          items: [],
          appliedChannels,
          entryRangeType: entryRangeTypeEnum.跟随父模板, //关联类型
        };
      });
    }

    // 下发模板中部分菜品
    if (this.issueMode === IssueModeEnum.下发模板中部分菜品) {
      const commonTemp = this.tempGoods?.[0];
      const { templateBrandId, templateName, templateId, appliedChannels = [] } = commonTemp || {};
      req.itemTemplateReqs = [
        {
          ...commonItemTemplateReq,
          brandId: templateBrandId,
          templateName: templateName + '_' + moment().valueOf(),
          relTemplateId: templateId,
          items: this.tempGoods?.map((good) => {
            const {
              brandName,
              firstCategoryId,
              firstCategoryName,
              goodsInfo,
              goodsPriceList,
              memberPrice,
              originalPrice,
              relationId,
              relationName,
              relationType,
              specName,
              spuId,
              templatePrice,
              unitName,
              rank,
              numMnemonicCode,
            } = good;
            return {
              brandName,
              firstCategoryId,
              firstCategoryName,
              goodsInfo,
              goodsPriceList,
              memberPrice,
              originalPrice,
              relationId,
              relationName,
              relationType,
              specName,
              spuId,
              templatePrice,
              unitName,
              rank,
              numMnemonicCode,
            };
          }),
          appliedChannels,
          entryRangeType: entryRangeTypeEnum.自定义实体关联范围,
        },
      ];
    }

    // 下发菜品库菜品
    if (this.issueMode === IssueModeEnum.下发菜品库菜品) {
      // 获取租户信息
      const userService = await getService();
      const { tenantId, rootOrgName } = userService.org;
      const templateName = `选菜下发_${rootOrgName}_${tenantId}_${moment().valueOf()}`;
      req.itemTemplateReqs = [
        {
          ...commonItemTemplateReq,
          brandId: this.brandId as number,
          templateName,
          items: this.goods.map((good) => {
            const {
              brandName,
              firstCategoryId,
              firstCategoryName,
              originalPrice,
              relationId,
              relationType,
              specName,
              wmSpuId,
              unitName,
              relationName,
              rank,
              brandId,
              salesChannels,
              secondCategoryId,
              secondCategoryName,
              memberPrice,
              channelDisplays,
              channelCategoryRel,
              goodsPriceList,
              templatePrice,
              numMnemonicCode,
              realGoodsType,
              //
            } = good?.relationType ? good : (transformTemplateRelationType(good) as IV);
            return {
              brandId,
              brandName,
              firstCategoryId,
              firstCategoryName,
              originalPrice,
              relationId,
              relationName,
              relationType,
              specName,
              spuId: wmSpuId,
              unitName,
              rank,
              salesChannels,
              secondCategoryId,
              secondCategoryName,
              memberPrice,
              channelDisplays,
              channelCategoryRel,
              goodsPriceList,
              templatePrice,
              numMnemonicCode,
              // 菜品自身真实的类型
              realGoodsType,
            };
          }),
          entryRangeType: entryRangeTypeEnum.自定义实体关联范围,
        },
      ];
    }

    // 分类下发
    if (this.isCategoryPublish) {
      // 获取租户信息
      const userService = await getService();
      const { tenantId, rootOrgName } = userService.org;
      const templateName = `分类下发_${rootOrgName}_${tenantId}_${moment().valueOf()}`;
      req.itemTemplateReqs = [
        {
          ...commonItemTemplateReq,
          brandId: this.brandId as number,
          templateName,
          templateCategories: transformCategoryDate(this.categoryList),
          entryRangeType: entryRangeTypeEnum.自定义实体关联范围,
        }
      ]
    }

    const res = await doAction(batchCreateTemplate(req));
    if(res) {
      this.templateId = this.isCategoryPublish ? [] : res?.map((temp: { id: string; }) => temp.id);
      if(this.isCategoryPublish) {
        const dataModule = this.wmChannel === WMChannelEnum.MT ? WMChannelEnum.MT_CATEGORY : WMChannelEnum.ELE_CATEGORY;
        const params: IssueSnapshotReq = {
          itemTemplateList: res.map((temp: { id: string; }) => temp.id),
          dataModule,
          targetList: this.poiList.map(poi => ({
            poiId: poi.poiId,
            orgId: getOrgId(poi),
          }))
        };
        
        // 后续期望先进行下发校验再创建下发数据快照
        
        const checkResult = await checkIssueWithBackup({
          ...(this.savePublishParams as IPostPublishMethods),
          itemTemplateList: res?.map((temp: { id: string; }) => temp.id),
        });
        if (!checkResult.pass) {
          this.loading = false
          return Modal.error({
            title: '错误',
            content: checkResult.msg,
          });
        };

        const issueSnapshotRes = await this.creatIssueSnapshot(params);
        return issueSnapshotRes;
      }
    }
    return res;
  }

  @action
  async creatIssueSnapshot(params: IssueSnapshotReq) {
    const res = await doAction(creatIssueSnapshot(params));
    if(res) {
      this.issueUniqueId = res.issueUniqueId;
    };
    return res;
  }

  @action
  setSaveParams() {
    const fieldStrategy: IV = {};
    const initialMoreFieldsValue = this.initialMoreFieldsValue;
    const fields = { ...initialMoreFieldsValue };
    
    const publishRuleValue = this.publishRuleFormRef?.getFieldsValue();
    const moreFieldsValue = this.moreFieldsFormRef?.getFieldsValue();
    
    // 当前选择的【集团菜发布规则】是否可设置更多字段；分类下发没有自定义更新字段弹窗
    const settableMoreFields = !this.isCategoryPublish && !!goodsPublishOptions.filter(
      (opt) => opt.code === publishRuleValue?.itemPublishRule
    )[0]?.moreFieldTip;

    if (settableMoreFields) {
      // 设置更多字段弹窗未打开则不会创建弹窗内form实例
      if (!moreFieldsValue) {
        const selectAllOuterMoreFields = publishRuleValue?.outerPreviewFields.length === outerPreviewFieldOpts.length;
        if (selectAllOuterMoreFields) {
          Object.keys(fields).forEach((key) => {
            // 需同时对应回填数据，弹窗外部数据全选时，未展示数据不一定都勾选
            fieldStrategy.wmFieldStrategy = {...fieldStrategy.wmFieldStrategy, [key]: initialMoreFieldsValue[key]};
          });
        } else {
          Object.keys(fields).forEach((key) => {
            // @ts-ignore
            fields[key] = _.pick(this.initialMoreFieldsValue[key], 'mode');            
            if (fields[key].mode === updateMode.部分更新) {
              fieldStrategy.wmFieldStrategy = {
                ...fieldStrategy.wmFieldStrategy,
                [key]: { ...initialMoreFieldsValue[key] },
              };
            } else {
              fieldStrategy.wmFieldStrategy = {
                ...fieldStrategy.wmFieldStrategy,
                [key]: fields[key],
              };
            }
          });
        }
      } else {
        Object.keys(fields).forEach((key) => {
          fields[key] = _.pick(moreFieldsValue[key], 'mode');          
          // 除部分更新外，过滤掉mode外的字段
          if (fields[key].mode !== updateMode.部分更新) {
            fieldStrategy.wmFieldStrategy = {
              ...fieldStrategy.wmFieldStrategy,
              [key]: fields[key],
            };
          } else {            
            // 只需要部分更新字段的勾选数据
            fieldStrategy.wmFieldStrategy = {...fieldStrategy.wmFieldStrategy, [key]: moreFieldsValue[key]};
          }
        });
      }
      fieldStrategy.wmFieldStrategy = _.omit(fieldStrategy.wmFieldStrategy, 'outerPreviewFields');
    };

    if(this.isCategoryPublish) {
      fieldStrategy.fieldStrategy = {
        category: {
          basicFields: publishRuleValue?.outerPreviewFields,
        },
      };
    };

    // 移除publishRuleValue中的多余的字段
    // @ts-ignore
    const omitKeys = [...Object.keys(initialMoreFieldsValue), 'outerPreviewFields'];
    const vals: { [index:string]: number } = _.omit(publishRuleValue, omitKeys);

    const formInfoKey = this.formInfo.map(item => item.fieldProps.name);
    // 兜底：避免下发当前下发方式不存在的规则字段
    Object.keys(vals).forEach(item => {
      if(!formInfoKey.includes(item)) {
        delete vals[item as string];
      }
    });    

    const itemRule: IV = {
      ...vals,
      ...fieldStrategy,
      // 针对5.70.10上线新增自定义字段的老前新后兼容问题的版本判断
      grayVersion: VERSION_NUMBER,
    };

    let dataModule = this.wmChannel;

    // 分类下发接口不传入issueMode，使用dataModule来区分是美团/饿了么分类下发
    if(!this.isCategoryPublish) {
      itemRule.issueMode = this.issueMode;
    }else {
      dataModule = this.wmChannel === WMChannelEnum.MT ? WMChannelEnum.MT_CATEGORY : WMChannelEnum.ELE_CATEGORY;
    };

    // 下发门店及生效时间
    const postPois = this.poiList.map((poi) => {
      let poiEffectiveTime;
      // 立即生效/指定时间生效
      poiEffectiveTime =
        publishRuleValue?.taskEffectiveType === taskEffectiveTypeOpts.immediate
          ? null
          : publishRuleValue?.menuEffectiveTime;
      return {
        poiId: poi.poiId,
        orgId: getOrgId(poi),
        effectiveTime: poiEffectiveTime,
      };
    });

    // 没有下发数据快照id则不需要传issueData，比如除分类下发，分类下发时的下发校验
    const issueData = this.issueUniqueId ? { sourceDataId: this.issueUniqueId } : undefined;

    const params: IWmPublishMethods = {
      effectiveType: publishRuleValue?.taskEffectiveType,
      itemRule,
      targetList: postPois,
      itemTemplateList: this.templateId,
      issueData,
      dataModule,
      deliverType: DeliverTypeEnum.Publish,
      issueType: IssueTypeEnum.PublishWithoutBackup,
    };

    if(!this.isCategoryPublish) {
      // 打印档口
      params.printStrategyList = [
        {
          rule: PrintRuleEnum.CONFIG,
          type: publishRuleValue?.printStallsName,
        },
      ];
    }

    this.savePublishParams = params;
    return params;
  }

  @action
  async savePublish() {
    const params = toJS(this.savePublishParams);
    
    // return;
    if(!this.isCategoryPublish) {
      // @ts-ignore
      const checkResult = await checkIssueWithBackup(params);
      if (!checkResult.pass) {
        this.loading = false
        return Modal.error({
          title: '错误',
          content: checkResult.msg,
        });
      };
    };

    return await WaiMaiPublishRules(params as IWmPublishMethods);
  }

  /**获取外卖整包下发复制信息 */
  @action
  async getCopyDetail(taskId: number, issueMode: IssueModeEnum) {
    this.loading = true;
    const realIssueMode = categoryDataModule.includes(this.wmChannel) ? IssueModeEnum.分类下发 : issueMode;
    this.issueMode = realIssueMode;
    const res = await fetchPkgPublishCopy({ taskId: +taskId, dataModule: this.wmChannel }).catch(
      () => {
        this.loading = false;
      }
    );
    let realWmChannel = this.wmChannel;
    if (realIssueMode === IssueModeEnum.分类下发) {
      realWmChannel = this.wmChannel === WMChannelEnum.MT_CATEGORY ? WMChannelEnum.MT : WMChannelEnum.ELE;
    };
    this.wmChannel = realWmChannel;
    if (res) {
      this.copyDetail = res;
      // 先获取税率开关
      await taxAndDepartmentModel.getTaxAndRate();
      this.setCopyDetail(res);
      this.loading = false;
    }
  }

  @action
  getTemplateId = (detail: IV) => {
    if (detail?.taskId) {
      const itemTemplateList = detail.itemTemplateList;
      if (!itemTemplateList?.length) {
        Modal.warning({
          title: '复制失败',
          content: `因数据有变动，该下发任务无法被复制，请在本页重新选择${this.isCategoryPublish ? '菜品分类数据后下发' : '菜单模板或菜品'}。`,
          cancelButtonProps: { hidden: true },
        });
        return null;
      }
      return itemTemplateList;
    }
  };

  /**用于复制接口后端字段转为表单项 */
  @action
  transformToFormValues(fieldStrategy?: any) {
    // 初始化基础字段
    const baseFields = {
      dish: { mode: updateMode.全部更新 },
      category: { mode: updateMode.全部更新 },
    };
    // 如果没有提供 fieldStrategy，直接返回基础字段
    if (!fieldStrategy) {
      return baseFields;
    }

    // 初始化 values 为 fieldStrategy 的副本
    let values: IV = { ...fieldStrategy };

    // 获取所有自定义字段的 code
    const outCustomFields = outerPreviewFieldOpts.map((opt) => opt.code);

    // 从 fieldStrategy.dish 中解构出 basicFields, advancedFields
    let { basicFields = [], advancedFields = [], salesFields = [] } = fieldStrategy.dish;
    const initialAdvanceFieldCodes = this.getInitAdvanceFields().map(item => item.code);

    // 只保留存在的值，比如说饿了么新品标签下掉了,订单显示税率开关关闭，这里会过滤掉
    advancedFields = advancedFields.filter((code: number) => initialAdvanceFieldCodes.includes(code))
    values = {
      ...values,
      dish: {
        ...values.dish,
        advancedFields,
      }
    };

    if(fieldStrategy.category.mode !== updateMode.部分更新) {
      values = { ...values, category: {...this.getInitialMoreFieldsValue()['category'], mode: fieldStrategy.category.mode}};
    };

    // 根据 fieldStrategy.dish.mode 的值，设置 values.outerPreviewFields
    switch (fieldStrategy.dish.mode) {
      case updateMode.全部更新:
        values.outerPreviewFields = outCustomFields;
        values = { ...values, dish: this.getInitialMoreFieldsValue()['dish'] }        
        break;
      case updateMode.全部不更新:
        values.outerPreviewFields = [];
        values = { ...values, dish: {...this.getInitialMoreFieldsValue()['dish'], mode: updateMode.全部不更新} }
        break;
      default:
        // 查找两者的公共元素
        values.outerPreviewFields = _.intersection(outCustomFields, [
          ...basicFields,
          ...advancedFields,
          ...salesFields,
        ]);
    }
    return values;
  };

  /** 分类下发自定义字段赋值 */
  @action
  transformCategoryOuterPreviewValue(fieldStrategy?: IV) {
    if(!fieldStrategy) {
      return {
        outerPreviewFields: categoryOuterPreviewFieldOpts.map(opt => opt.code)
      }
    };

    const value = fieldStrategy.category.basicFields;
    return {
      outerPreviewFields: value,
    };
  };

  @action
  handleCopyPoi = async (poiList: IV[]) => {
    let poiIds: number[] = poiList.map((poi) => poi?.poiId);
    if (this.retry) {      
      const failPoiList = poiList.filter((poi: { status: string }) => poi.status !== 'SUCCEED');
      poiIds = failPoiList.map((poi: { poiId: number }) => poi.poiId);
      this.retry = false;
    };

    // 获取现在已绑定外卖平台的门店
    const boundPoiIds = await this.fetchBoundPoiMap();
    // 过滤下发详情中已解绑的门店
    const filterUnBoundPoi = poiIds?.filter((poi) => boundPoiIds.includes(Number(poi)));
    // 去重门店
    const uniquePois = Array.from(new Set(filterUnBoundPoi));    
    // 获取门店详情
    this.queryPoiList(uniquePois, true);
  };

  /** 查询菜单模板数据 */
  @action
  getTemplateInfo = async (ids: string[], targetList?: IV[]) => {
    this.tempTableLoading = true;
    const params = {
      templateType:
        this.wmChannel === WMChannelEnum.MT ? WmTemplateTypeEnum.美团 : WmTemplateTypeEnum.饿了么,
      templateIds: ids,
      queryEntryFlag: 1,
      needDetails: true,
      isCopyTask: true,
    };
    
    try {
      // @ts-ignore
      const res = await templateService.fetchFullTemplateList(params);
      if (res) {
        // 单模板可从复制接口中获取关联门店
        let poiList = targetList || [];
        if(res.templateList?.length > 1) {
          // 多模板需从菜单模板中取关联门店
          poiList = res.templateList.map((item: { refPoiList: any[]; }) => item?.refPoiList).flat();
        };        
        
        this.handleCopyPoi(poiList)
        const handelTemplateList = res.templateList?.map(
          (t: { relTemplateId: string; relTemplateName: string }) => ({
            ...t,
            id: t.relTemplateId,
            templateName: t.relTemplateName,
          })
        );
        this.templateList = handelTemplateList;
      }
      this.tempTableLoading = false;
    } catch (e) {
      this.tempTableLoading = false;
      Modal.error({
        title: '错误',
        content: e?.message,
      })
    }
  };

  /** 复制下发菜单模板中部分菜的信息 */
  @action
  getTempGoodsInfo = async (ids: string[]) => {
    this.goodsLoading = true;
    const params = {
      templateType:
        this.wmChannel === WMChannelEnum.MT ? WmTemplateTypeEnum.美团 : WmTemplateTypeEnum.饿了么,
      templateIds: ids,
      queryEntryFlag: 1,
      needDetails: true,
      needMnemonicCode: this.abStrategyMap[ABtestEnum.味千],
    };
    try {
      // @ts-ignore
      const result = await templateService.fetchFullTemplateList(params);
      if (result) {
        const temp = result.templateList[0];
        const goods = (temp?.items || []).map((good: WaimaiSkuItem) => ({
          ...good,
          id: good.relationId,
          wmSpuId: good.spuId,
          wmSkuId: good.relationId,
          templateBrandId: temp?.brandId,
          templateId: temp.relTemplateId,
          templateName: temp.relTemplateName,
          refPoiList: temp.refPoiList,
          name: good.relationName,
          type: good.realGoodsType,
        }));
        this.tempGoods = goods;
        this.goodsLoading = false;
      }
    } catch (e) {
      this.goodsLoading = false;
      Modal.error({
        title: '错误',
        content: e?.message,
      });
    }
  };

  @action
  getDishInfo = async (ids: string[]) => {
    this.goodsLoading = true;
    const params = {
      templateType:
        this.wmChannel === WMChannelEnum.MT ? WmTemplateTypeEnum.美团 : WmTemplateTypeEnum.饿了么,
      templateIds: ids,
      queryEntryFlag: 1,
      needDetails: true,
      needMnemonicCode: this.abStrategyMap[ABtestEnum.味千],
    };
    try {
      // @ts-ignore
      const result = await templateService.fetchFullTemplateList(params);
      if (result) {
        const temp = result.templateList[0];
        const goods = (temp?.items || []).map(
          (good: WaimaiSkuItem & { realGoodsType: number }) => ({
            ...good,
            id: good.relationId,
            wmSpuId: good.spuId,
            wmSkuId: good.relationId,
            name: good.relationName,
            price: good.originalPrice,
            basePrice: good.originalPrice,
            categoryName: good.secondCategoryName
              ? `${good.firstCategoryName}/${good.secondCategoryName}`
              : good.firstCategoryName,
            type: good.realGoodsType,
          })
        );
        this.goods = goods;
        this.brandId = temp.brandId;
        this.goodsLoading = false;
      }
    } catch (e) {
      this.goodsLoading = false;
      Modal.error({
        title: '错误',
        content: e?.message,
      });
    }
  };

  @action
  getCategoryInfo = async (ids: string[]) => {
    this.goodsLoading = true;
    const params = {
      templateType:
        this.wmChannel === WMChannelEnum.MT
          ? WmTemplateTypeEnum.美团分类下发
          : WmTemplateTypeEnum.饿了么分类下发,
      templateIds: ids,
      queryEntryFlag: 1,
      needDetails: true,
      queryPoiType: 1,
    };
    try {
      // @ts-ignore
      const result = await templateService.fetchFullTemplateList(params);
      if (result) {
        const temp = result.templateList[0];
        const category = (temp?.templateCategories || []).map(
          (item: {
            categoryId: number;
            desc: string;
            categoryPropertyType: CategoryPropertyEnum;
          }) => ({
            ...item,
            id: item.categoryId,
            description: item.desc,
            propertyType: item.categoryPropertyType,
          })
        );
        if(!category?.length) {
          Modal.warning({
            title: '复制失败',
            content: '由于菜品分类已被全部删除，该下发任务无法被复制，请在本页重新选择数据后下发',
            cancelButtonProps: { hidden: true },
          });
        };
        this.categoryList = category;
        this.brandId = temp.brandId;
        this.goodsLoading = false;
      }
    } catch (e) {
      this.goodsLoading = false;
      Modal.error({
        title: '错误',
        content: e?.message,
      });
    }
  };

  /** 处理checkBox样式状态 */
  @action
  handleCheckBoxStatus(value: IV, isDefaultSetting?: boolean) {
    const fieldList: OuterUpdateFieldsInfo[] = [
      {
        fields: 'dish',
        options: this.getInitDishBasicFields(),
        key: CheckBoxFieldEnum.菜品基础信息,
        fieldKey: 'basicFields',
      },
      {
        fields: 'dish',
        options: wmDishDetailOpts,
        key: CheckBoxFieldEnum.菜品详细信息,
        fieldKey: 'detailedFields',
      },
      {
        fields: 'dish',
        options: this.getInitSaleFields(),
        key: CheckBoxFieldEnum.菜品售卖信息,
        fieldKey: 'salesFields',
      },
      {
        fields: 'dish',
        options: this.getInitAdvanceFields(),
        key: CheckBoxFieldEnum.菜品高级信息,
        fieldKey: 'advancedFields',
      },
      {
        fields: 'category',
        options: wmDishCategoryOpts,
        key: CheckBoxFieldEnum.菜品分类基础信息,
        fieldKey: 'basicFields',
      },
    ];
    fieldList.forEach((v) => {
      const fieldData = value[v.fields];
      if (fieldData.mode === updateMode.部分更新) {
        const checkedValue = fieldData[v.fieldKey]?.length === v.options.length;
        const indeterminateValue = !!fieldData[v.fieldKey]?.length && fieldData[v.fieldKey]?.length < v.options.length;
        if(isDefaultSetting) {
          this.defaultCheckedMap[v.key] = checkedValue;
          this.defaultIndeterminateMap[v.key] = indeterminateValue;
        }else {
          this.checkedMap[v.key] = checkedValue;
          this.indeterminateMap[v.key] = indeterminateValue;
        }
      } else {
        if(isDefaultSetting) {
          this.defaultCheckedMap[v.key] = initCheckedMap[v.key];
          this.defaultIndeterminateMap[v.key] = initIndeterminateMap[v.key];
        }else {
          this.checkedMap[v.key] = initCheckedMap[v.key];
          this.indeterminateMap[v.key] = initIndeterminateMap[v.key];
        }
      }
    });
  }

  /** 查询绑定美团或饿了么的门店 */
  @action
  fetchBoundPoiMap = async () => {
    const wmSource =
      this.wmChannel === WMChannelEnum.MT ? WaimaiSourceEnum.MEITUAN : WaimaiSourceEnum.ELEME;
    const service = new WaimaiService(wmSource);
    const boundPoiIds = await service.fetchAllBoundPoiIds(+wmSource === WaimaiSourceEnum.MEITUAN);
    return boundPoiIds;
  };

  /** 下发规则数据回填处理 */
  @action
  handleRuleData(detail: IV) {
    const { rule, effectiveType } = detail;

    // 所有下发桂策，包括自定义更新字段的数据
    const ruleValue = JSON.parse(rule);
    // 处理自定义更新字段数据，构造自定义更新字段弹窗外部的数据；分类下发自定义更新字段只在外部展示，没有弹窗
    const outerFieldsValue = this.isCategoryPublish
      ? this.transformCategoryOuterPreviewValue(ruleValue?.fieldStrategy)
      : this.transformToFormValues(ruleValue?.wmFieldStrategy);

    // 打印档口字段数据转换
    if (detail.printRule) {
      ruleValue.printStallsName = Number(detail.printRule);
    }
    
    // 除自定义更新字段弹窗中的字段数据（包含外部下发规则和外层的自定义字段）
    const formValues = {
      ...ruleValue,
      taskEffectiveType: effectiveType,
      ...outerFieldsValue,
    };

    // 删除下发规则中的自定义更新字段数据
    const publishRule = _.omit(formValues, ['wmFieldStrategy']);
    this.publishRuleFormRef?.setFieldsValue(publishRule);

    if(!this.isCategoryPublish) {
      // 自定义更新字段弹窗中的checkbox状态处理
    this.handleCheckBoxStatus(outerFieldsValue);

    // 自定义更新数据赋值
    this.moreFieldsFormRef ? this.moreFieldsFormRef.setFieldsValue(outerFieldsValue) : (this.initialMoreFieldsValue = outerFieldsValue);
    };
  }

  /** 获取菜单模板和菜品数据 */
  @action
  handleTempAndDishInfo(detail: IV) {
    const templateIdList = this.getTemplateId(detail);
    // 获取菜单模板
    if (this.issueMode === IssueModeEnum.按模板下发菜品) {
      templateIdList && this.getTemplateInfo(templateIdList, detail.targetList);
    } else if (this.issueMode === IssueModeEnum.下发模板中部分菜品) {
      templateIdList && this.getTempGoodsInfo(templateIdList);
    } else if (this.issueMode === IssueModeEnum.下发菜品库菜品) {
      templateIdList && this.getDishInfo(templateIdList);
    }else {
      templateIdList && this.getCategoryInfo(templateIdList);
    }
  }

  // 根据后端返回的下发详情进行初始值设置
  @action
  async setCopyDetail(detail: any) {
    if (detail?.taskId) {
      try {
        const { targetList } = detail;

        // 按模板下发需要特殊处理，主要处理下发多模板的情况
        if(this.issueMode !== IssueModeEnum.按模板下发菜品) {
          this.handleCopyPoi(targetList);
        }
        
        // 下发规则
        this.handleRuleData(detail);
        // 菜单模板和菜品
        this.handleTempAndDishInfo(detail);
      } catch (e) {
        message.error(e);
      }
    }
  }

  getInitSaleFields() {
    return this.wmChannel === WMChannelEnum.MT ? MTwmDishSaleOpts : ELEwmDishSaleOpts;
  }

  getInitAdvanceFields() {
    const fields = wmDishAdvanceInfoOpts;

    return fields.filter(item => {
      // 订单显示税率开关关闭时不展示税目，需要操作前先获取到开关数据
      if (item.code === WmCustomMoreFields.税目 && taxAndDepartmentModel.taxRateSwitchStatus !== TaxRateSwitchStatusEnum.ON) {
        return false;
      }

      return true;
    })
  }

  getInitDishBasicFields() {
    return wmDishBasicOpts;
  }

  /**
   * 
   * @returns 根据选择的渠道获取默认值
   */
  @action
  getInitialMoreFieldsValue() {
    const disSaleOpts = this.getInitSaleFields();
    const dishAdvanceOpts = this.getInitAdvanceFields();
    const dishBasicOpts = this.getInitDishBasicFields();
    return {
      dish: {
        mode: updateMode.全部更新,
        basicFields: dishBasicOpts.map((opt) => opt.code),
        detailedFields: wmDishDetailOpts.map((opt) => opt.code),
        salesFields: disSaleOpts.map((opt) => opt.code),
        advancedFields: dishAdvanceOpts.map((opt) => opt.code),
      },
      category: {
        mode: updateMode.全部更新,
        basicFields: wmDishCategoryOpts.map((opt) => opt.code),
      },
    } as InitMoreFields;
  };

  /**
   * 查询和设置下发门店数量限制
   */
  @action
  setIssueNumLimitConfig = async () => {
    const userService = await getService();
    const param = {
      tenantId: String(userService.getOrgInfo()?.tenantId),
      type: IssueLimitConfigType.WM,
    }

    const res = await getIssueNumLimitConfig(param);
    this.issueLimitNumConfig = res;
  }

  /** 转换一下，方便后续操作套餐子菜联动的时候跟菜单模板使用同一套函数 */
  transGoodsListItem = (items: WaimaiSkuItem[]) => {
    return items.map(item => ({
      ...item,
      wmSkuId: item.relationId,
      name: item.relationName,
      type: item.realGoodsType,
    }))
  };

  /**
   * 查询模板菜品列表, 使用跟选择模板菜品选择器相同的接口，做相同的数据转换
   * @param templateId
   * @returns
   */
  getTemplateGoods = async (templateId: string) => {
    const res = await templateService.getTemplateGoodsDetail({
      templateId,
      templateType:
        this.wmChannel === WMChannelEnum.MT ? TemplateTypeEnum.美团 : TemplateTypeEnum.饿了么,
      needMnemonicCode: !!this.abStrategyMap?.[ABtestEnum.味千],
    });

    if (res?.items) {
      return res.items.map((item) => ({
        ...item,
        templateName: res?.templateName,
        templateId: res.id,
        appointedBrandId: res?.brandId,
        templateBrandId: res?.brandId,
        templateBrandName: res.brandName,
        categoryId: item.firstCategoryId,
        refPoiList: res.refPoiList,
        desc: res.desc,
        // 以下转换，方便后续操作套餐子菜联动的时候跟菜单模板使用同一套函数
        wmSkuId: item.relationId,
        wmSpuId: item.spuId,
        name: item.relationName,
        type: item.realGoodsType,
      }));
    }
  };
}

export default WMPkgPublish;
export const wmPkgPublishModel = new WMPkgPublish();
