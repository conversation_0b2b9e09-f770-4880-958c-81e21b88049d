import * as React from 'react';
import { Empty, Icon, message, Modal, Spin, Tag, Tree } from '@mtfe/sjst-antdx';
import { Alert } from '@mtfe/sjst-antdx-saas';
import { getMethodService } from '@rms-goods/root/src/services/goodsMethod';
import { getSideService } from '@rms-goods/root/src/services/goodsSide';
import { getSpecService } from '@rms-goods/root/src/services/goodsSpec';
import styles from './index.module.less';
import {
  checkIsExistRule,
  copyNodes,
  doRankList,
  GroupBoxEnum,
  listToGroup,
  listToTree,
  PROP_KEYS,
  PropertItem,
  PropertTypeEnum,
  renderTreeNode,
  ResItem,
} from './utils';
import util from '@rms-goods/utils/goods/method';
import { ContextProps, wrapContext } from '@mtfe/next-biz/es/contexts/user';

export interface IProps extends ContextProps {
  /** 控制弹窗是否显示 */
  visiable: boolean;
  title?: string | React.ReactNode;
  width?: string | number;
  /** 外部容器id, 弹窗挂载使用, 默认挂载 rms-goods */
  containerId?: string;
  /** 编辑情况下的分组数据 */
  groupData?: ResItem[][];
  /** 需要在保存时进行校验的规则, 如果是编辑情况下需要排除当前值（需要是外部已存的规则）: 示例: [[[{...}, {...}], [{...}]], [[{...}],[{...}]]] */
  checkRules: ResItem[][][];
  /** 外部传入的数据, 传入列表 / 回调即可, 会根据groupId自动聚合分组, 入参请保持对应顺序 */
  dataMap?: { [key in PropertTypeEnum]?: PropertItem[] | (() => PropertItem[]) };
  /** loadDataMap已经废弃使用 --需要加载全量数据的map, 加载全量数据优先级高于dataMap */
  loadDataMap?: { [key in PropertTypeEnum]?: boolean };
  /** 确定函数, 会吐出对应值或者undefined(校验失败吐出undefined) */
  onOk: (data?: ResItem[][]) => void;
  /** 关闭函数 */
  onCancel: () => void;
}

const defaultTitle = '设置互斥规则';

/** 不支持加载全量的接口, 设置最大值来加载全部 */
const defaultQueryAll = {
  pageNo: 1,
  pageSize: 999999,
};

/** 拼接样式 */
const GroupBoxClassName = {
  left: styles['property-rule-modal-flex-box-left'] + ' ' + styles['left-border'],
  right: styles['property-rule-modal-flex-box-right'] + ' ' + styles['right-border'],
};

const emptyContent = (
  <>
    <Empty
      image={Empty.PRESENTED_IMAGE_SIMPLE}
      className={styles['empty-content']}
      description={
        <div>
          <div>请添加至少一项属性</div>
          <div>同组内属性不互斥</div>
        </div>
      }
    />
  </>
);

/** 设置属性互斥规则弹窗 */
const CreateOrEditPropertyRuleModal = (props: IProps) => {
  const { visiable, dataMap, loadDataMap, groupData, checkRules, title, width, containerId, onCancel, onOk } = props;
  /**
   * 组件大致实现思路:
   * 1. 外部获取或拉取全部数据: 确定所有叶子节点
   * 2. 三层结构: root->node->leaf, 规格比较特殊, root->leaf
   * 3. 根据数据确定根节点, 根据叶子节点聚合为分组, 分组挂载在根节点上形成三层树结构
   * 4. 通过上述实现支持叶子节点全部消失后联动节点, 根节点消失的交互
   * 5. 通过受控的expandKeys保证节点收缩交互
   */
  /** 所有的树的叶子节点数据 */
  const [treeNodeList, setTreeNodeList] = React.useState<PropertItem[]>([]);
  /** 存放节点类型, 作为构造树的根节点 */
  const [nodeTypes, setNodeTypes] = React.useState<PropertTypeEnum[]>([]);
  /** 两个分组中的数据 */
  const [group1, setGroup1] = React.useState<PropertItem[]>([]);
  const [group2, setGroup2] = React.useState<PropertItem[]>([]);
  /** 当前需要渲染的树节点, 用于构造显示树 */
  const [curNodeList, setCurNodeList] = React.useState<PropertItem[]>([]);
  /** 用于校准展示顺序: PRD要求 */
  const [rankList, setRanList] = React.useState<(number | string)[]>([]);
  /** 左侧树节点加载, 优化体验 */
  const [loading, setLoading] = React.useState(false);

  const fetchTreeData = React.useCallback(
    async (dataMap?: IProps['dataMap'], loadDataMap?: { [key in PropertTypeEnum]?: boolean }, groupData?: ResItem[][]) => {
      setLoading(true);
      const list: PropertItem[] = [];
      const types: PropertTypeEnum[] = [];
      const rank: (string | number)[] = [];
      // 遍历确定树结构, 兼容了后续的做法
      for (const key of PROP_KEYS) {
        let curList: PropertItem[] = [];
        // a.[1] 等于 a.1, js会自动给数字转为字符串属性值进行访问
        if (loadDataMap?.[key]) {
          // 1. 加载数据优先级最高
          let res;
          try {
            if (key === PropertTypeEnum.做法) {
              const result = await (await getMethodService()).getMethodList(defaultQueryAll);
              res = {
                // 做法列表中分组也会存在, 但是没有id, 所以需要使用id来过滤
                items: util.methodTO2SpuMethodTO(result.items ?? [])?.filter((item) => item?.id),
              };
            } else if (key === PropertTypeEnum.加料) {
              res = await (await getSideService()).fetchSideSpus(defaultQueryAll);
            } else if (key === PropertTypeEnum.规格) {
              res = await (await getSpecService()).getGoodsSpecsList(defaultQueryAll);
              // @ts-ignore
              res.items = res.items?.map(({ specId, name: specName }) => ({ specId, specName, id: specId }));
            }
          } catch {
            // 接口报错的边界情况下的兜底
            res = { items: [] };
          }
          curList = res?.items ?? [];
        } else if (Array.isArray(dataMap?.[key]) && dataMap?.[key]?.length) {
          // 2. 不加载数据, 并且外部传入了有效值, 使用外部值, 并且追加类型
          curList = (dataMap?.[key] as PropertItem[]) ?? [];
        } else if (dataMap?.[key] && typeof dataMap?.[key] === 'function') {
          curList = (dataMap?.[key] as () => PropertItem[])() ?? [];
        }

        // 3. 如果当前列表存在值, 那么就进行追加
        if (curList?.length) {
          // 存储叶子节点
          list.push(...copyNodes(curList, key));
          // 存储类型
          types.push(key);
        }
      }

      // 对list遍历来追加key
      list.forEach((node) => {
        // 追加排序
        rank.push(`${node.propertyType}-${node.id}` ?? '');
      });
      // 初始化转换, 获取当前所有group的实体
      if (groupData?.length) {
        const [g1, g2] = groupData;
        console.log(list, groupData);
        // 通过id和type来寻找到目标节点, 保证回显, 防止仅id判断导致的重复问题
        // 不会出现找不到的情况, 使用as防止类型报错
        const group1 = (g1 ?? [])
          .map((item) => list?.find((node) => node.id === item.propertyId && node.propertyType === item.propertyType) as PropertItem)
          .filter((item) => Boolean(item));
        const group2 = (g2 ?? [])
          .map((item) => list?.find((node) => node.id === item.propertyId && node.propertyType === item.propertyType) as PropertItem)
          .filter((item) => Boolean(item));
        setGroup1(group1);
        setGroup2(group2);
      }
      // 设置树的叶子节点, 后续通过叶子节点构造树
      setTreeNodeList([...list]);
      // 设置当前的树节点
      setCurNodeList([...list]);
      // 设置数据来源类型, 后续作为树的根节点
      setNodeTypes(types);
      // 设置排序
      setRanList(rank);
      setLoading(false);
    },
    []
  );

  /** 会根据dataList中的type来进行区分处理, 因为group1和group2变化比较频繁, 所以没必要进行缓存, 而是使用hooks的函数式更新 */
  const handleDrop = React.useCallback(
    (targetGroup: GroupBoxEnum, strData: string) => {
      try {
        const data = JSON.parse(strData);
        if (!data) return;

        let resList: PropertItem[] = [];

        // 本次新增nodeList
        if (!Array.isArray(data.children)) {
          /** 如果是叶子节点, 或者没有有效children: 是叶子节点, 直接追加处理 */
          resList = [data];
        } else {
          /** 非叶子节点, 就需要遍历当前节点的所有叶子节点 */
          // 1. 定义递归对列, 并塞入当前节点作为头结点
          const list: PropertItem[] = [data];
          // 2. 对队列进行递归(深度优先)
          while (list?.length) {
            // 3. 取当前节点, 存在有效children就将children都追加到队列, 没有的话证明当前节点是叶子节点, 追加到结果数组
            const curItem = list.shift();
            // 特殊兜底情况, 基本不会出现
            if (!curItem) return;
            if (Array.isArray(curItem?.children) && curItem?.children?.length) {
              list.push(...(curItem?.children ?? []));
            } else {
              // 4. 说明是叶子节点, 追加到结果数组
              resList.push(curItem);
            }
          }
        }
        // 汇总当前分组的所有node
        if (targetGroup === GroupBoxEnum.属性组1) {
          setGroup1((preList) => doRankList([...preList, ...resList], rankList));
        } else {
          setGroup2((preList) => doRankList([...preList, ...resList], rankList));
        }
      } catch (e) {
        // 没有数据或解析失败的特殊情况, 无效拖拽
      }
    },
    [rankList]
  );

  // 移除tag函数
  const handleCloseTag = React.useCallback((targetGroup: GroupBoxEnum, removeId?: string | number) => {
    if (targetGroup === GroupBoxEnum.属性组1) {
      setGroup1((preList) => [...preList?.filter((item) => item?.id !== removeId)]);
    } else {
      setGroup2((preList) => [...preList?.filter((item) => item?.id !== removeId)]);
    }
  }, []);

  const handleClearGroup = React.useCallback((targetGroup: GroupBoxEnum) => {
    if (targetGroup === GroupBoxEnum.属性组1) {
      setGroup1([]);
    } else {
      setGroup2([]);
    }
  }, []);

  React.useEffect(() => {
    if (visiable) {
      // 1. 根据传入的值确定树结构
      fetchTreeData(dataMap, loadDataMap, groupData);
    } else {
      // 弹窗关闭了, 就清空对应值
      setTreeNodeList([]);
      setCurNodeList([]);
      setNodeTypes([]);
      setRanList([]);
      setGroup1([]);
      setGroup2([]);
    }
  }, [visiable, dataMap, loadDataMap, groupData]);

  React.useEffect(() => {
    const group1Ids = group1.map((item) => item.id);
    const group2Ids = group2.map((item) => item.id);
    const hideIds = [...group1Ids, ...group2Ids];
    const curList = treeNodeList.filter((item) => !hideIds?.includes(item.id));
    setCurNodeList(curList);
  }, [treeNodeList, nodeTypes, group1, group2]);

  const renderTextIcon = () => (
    <div className={styles['property-rule-modal-background-box']}>
      <div className={styles['property-rule-modal-background-box-content']}>互斥</div>
      <div className={styles['property-rule-modal-background-box-hidden']}></div>
    </div>
  );

  const renderTree = () => (
    <div className={styles['property-rule-modal-flex-box-tree-box']}>
      <div className={styles['title']}>
        <span className={styles['title-main']}>规格/做法/加料</span>
        <span className={styles['title-extra']}>拖拽属性至右侧分组</span>
      </div>
      <div className={styles['content-box']}>
        {/* 加个缓存节点来优化用户体验 */}
        <Spin spinning={loading}>
          <Tree
            draggable
            selectable={false}
            className={styles['content-box-tree']}
            // 规格不默认展开
            defaultExpandedKeys={[`${PropertTypeEnum.做法}`, `${PropertTypeEnum.加料}`]}
            onDragStart={({ event, node }) => {
              if (node?.props?.data?.length) {
                // @ts-ignore: 忽略报错, 其实事件是支持的, 而且对应props也追加了data字段
                event.dataTransfer.setData('data', node?.props?.data);
              }
            }}
          >
            {renderTreeNode(listToTree(curNodeList, nodeTypes))}
          </Tree>
        </Spin>
      </div>
    </div>
  );

  const renderGroup1 = () => (
    <div
      className={GroupBoxClassName.left}
      onDrop={(e) => {
        handleDrop(GroupBoxEnum.属性组1, e?.dataTransfer?.getData('data') ?? '');
      }}
      onDragOver={(e) => e.preventDefault()}
    >
      <div className={styles['title-left']}>
        属性组1
        <span className={styles['clear-btn']} onClick={() => handleClearGroup(GroupBoxEnum.属性组1)}>
          <Icon type='delete' />
          清空
        </span>
      </div>
      {/* 已兼容数据过多时溢出的边界场景 */}
      <div className={styles['content-box']}>
        {group1?.length ? (
          <>
            {listToGroup(group1).map((group, index) => {
              return (
                <div key={`${group?.id}-${group.propertyType}`}>
                  {/* 除第一个以外的其他间距都需要大一些, 下面的也是这样子 */}
                  <div className={index ? styles['content-box-group-name'] : styles['content-box-group-name-first']}>
                    {group.propertyType === PropertTypeEnum.规格
                      ? PropertTypeEnum[PropertTypeEnum.规格]
                      : // 存在普通菜分组名称为空的情况
                        `${PropertTypeEnum[group.propertyType as PropertTypeEnum]}-${group?.name ?? ''}`}
                    ：
                  </div>
                  <div>
                    {group?.children?.map((item) => (
                      <Tag
                        closable
                        onClose={() => handleCloseTag(GroupBoxEnum.属性组1, item?.id)}
                        className={styles['tag']}
                        key={`${item?.id}-${item.propertyType}`}
                      >
                        {item.name}
                      </Tag>
                    ))}
                  </div>
                </div>
              );
            })}
          </>
        ) : (
          emptyContent
        )}
      </div>
    </div>
  );

  const renderGroup2 = () => (
    <div
      className={GroupBoxClassName.right}
      onDrop={(e) => {
        handleDrop(GroupBoxEnum.属性组2, e?.dataTransfer?.getData('data') ?? '');
      }}
      onDragOver={(e) => e.preventDefault()}
    >
      <div className={styles['title-right']}>
        属性组2
        <span className={styles['clear-btn']} onClick={() => handleClearGroup(GroupBoxEnum.属性组2)}>
          <Icon type='delete' />
          清空
        </span>
      </div>
      <div className={styles['content-box']}>
        {group2?.length ? (
          <>
            {listToGroup(group2).map((group, index) => {
              return (
                <div key={`${group?.id}-${group.propertyType}`}>
                  <div className={index ? styles['content-box-group-name'] : styles['content-box-group-name-first']}>
                    {group.propertyType === PropertTypeEnum.规格
                      ? PropertTypeEnum[PropertTypeEnum.规格]
                      : // 存在普通菜分组名称为空的情况
                        `${PropertTypeEnum[group.propertyType as PropertTypeEnum]}-${group?.name ?? ''}`}
                    ：
                  </div>
                  <div>
                    {group?.children?.map((item) => (
                      <Tag
                        closable
                        onClose={() => handleCloseTag(GroupBoxEnum.属性组2, item?.id)}
                        className={styles['tag']}
                        key={`${item?.id}-${item.propertyType}`}
                      >
                        {item.name}
                      </Tag>
                    ))}
                  </div>
                </div>
              );
            })}
          </>
        ) : (
          emptyContent
        )}
      </div>
    </div>
  );

  const alertMsgEl = React.useMemo(() => {
    return (
      <>
        请把需要互斥的属性分别放到两个属性组里，两个组之间的属性在点餐时不可同时选中；同一组中的属性不互斥；规则未覆盖的属性默认可以同时选中。
        <span style={{ fontWeight: 700 }}>注：规格互斥当前仅对手机点餐生效</span>
      </>
    );
  }, []);

  return (
    <Modal
      // 点击蒙层不允许关闭
      maskClosable={false}
      visible={visiable}
      title={title ?? defaultTitle}
      width={width ?? 800}
      onCancel={onCancel}
      getContainer={() => document.getElementById(containerId || 'rms-goods') as HTMLDivElement}
      className={styles['property-rule-modal']}
      onOk={() => {
        // 1. 判空
        if (!group1?.length || !group2?.length) {
          message.error('每个组内至少要添加一项属性，请操作');
          return;
        }

        // 转换为接口需要的格式
        const resGroup1: ResItem[] = group1?.map((item) => ({
          propertyId: item.id,
          propertyType: item.propertyType,
          propertyName: item.name,
        }));
        const resGroup2 = group2?.map((item) => ({
          propertyId: item.id,
          propertyType: item.propertyType,
          propertyName: item.name,
        }));
        // 2. 判断重复
        /**
         * 校验重复:
         * 1. 两个组中选中项完全一致（不区分是在哪个属性组） 提示文案
         * 2. 新建规则是已有规则子集的（不区分是在哪个属性组）提示文案
         */
        let msg = '';
        const notNext = (checkRules ?? [])?.some((rule) => {
          const checkMsg = checkIsExistRule([resGroup1, resGroup2], rule);
          if (checkMsg?.length) {
            msg = checkMsg;
            return true;
          }
        });
        if (notNext) {
          message.error(msg);
          return;
        }
        // 两个分组中都出现了规格实体
        const isSpecMutex =
          resGroup1.some((prop) => prop.propertyType === PropertTypeEnum.规格) &&
          resGroup2.some((prop) => prop.propertyType === PropertTypeEnum.规格);
        if (isSpecMutex) {
          message.error('不支持规格与规格互斥，请修改');
          return;
        }

        onOk([resGroup1, resGroup2]);
      }}
    >
      <div>
        <Alert type='warning' showIcon message={alertMsgEl} />
        <div className={styles['property-rule-modal-flex-box']}>
          {renderTree()}
          {renderGroup1()}
          {renderTextIcon()}
          {renderGroup2()}
        </div>
      </div>
    </Modal>
  );
};

export default wrapContext<IProps>(CreateOrEditPropertyRuleModal);
